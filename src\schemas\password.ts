import { z } from "zod";

// Schema for password update (for users with existing password)
export const PasswordUpdateSchema = z
  .object({
    currentPassword: z
      .string()
      .min(1, { message: "Password saat ini wajib diisi" }),
    newPassword: z
      .string()
      .min(6, { message: "Password baru minimal 6 karakter" }),
    confirmPassword: z
      .string()
      .min(1, { message: "Konfirmasi password wajib diisi" }),
  })
  .refine((data) => data.newPassword === data.confirmPassword, {
    message: "Password baru dan konfirmasi password tidak sama",
    path: ["confirmPassword"],
  });

// Schema for setting password (for Google OAuth users without password)
export const PasswordSetSchema = z
  .object({
    currentPassword: z.string().optional(), // Optional for Google OAuth users
    newPassword: z
      .string()
      .min(6, { message: "Password baru minimal 6 karakter" }),
    confirmPassword: z
      .string()
      .min(1, { message: "Konfirmasi password wajib diisi" }),
  })
  .refine((data) => data.newPassword === data.confirmPassword, {
    message: "Password baru dan konfirmasi password tidak sama",
    path: ["confirmPassword"],
  });
