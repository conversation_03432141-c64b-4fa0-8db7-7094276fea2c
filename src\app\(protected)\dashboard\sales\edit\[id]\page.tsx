import React from "react";
import { notFound } from "next/navigation";
import EnhancedSaleEditPage from "@/components/pages/dashboard/sales/edit";
import { getSaleById } from "@/actions/entities/sales";
import { getProducts } from "@/lib/get-products";
import DashboardLayout from "@/components/layout/dashboardlayout";
import { Metadata } from "next";
import { Product } from "@/components/pages/dashboard/sales/new/types";

export const metadata: Metadata = {
  title: "Edit Penjualan - Kasir Online",
  description: "Edit transaksi penjualan",
};

type PageProps = {
  params: Promise<{ id: string }>;
  searchParams?: Promise<Record<string, string | string[]>>;
};

export default async function SaleEdit(props: PageProps) {
  // Get the id from params (which is now a Promise)
  const params = await props.params;
  const id = params.id;

  // Fetch the sale with the given ID
  const saleResult = await getSaleById(id);

  // If sale not found, return 404
  if (!saleResult.sale) {
    notFound();
  }

  // Fetch products for the form
  const fetchedProducts = await getProducts({
    includeOutOfStock: true, // Show all products for editing
    orderBy: "createdAt", // Order by creation date to get newest products
    orderDirection: "desc", // Newest first
    excludeDrafts: true, // Exclude draft products
  });

  // Adapt the products to match the expected Product interface
  const adaptedProducts: Product[] = fetchedProducts.map((product) => ({
    id: product.id,
    name: product.name,
    price: product.price,
    stock: product.stock || 0,
    image: product.image || undefined,
    createdAt: product.createdAt || undefined,
  }));

  // Create a basic serialized sale object with the minimum required fields
  const serializedSale = {
    id: saleResult.sale.id,
    totalAmount: saleResult.sale.totalAmount,
    saleDate: saleResult.sale.saleDate.toISOString(),
    items: saleResult.sale.items.map((item) => ({
      id: item.id,
      quantity: item.quantity,
      priceAtSale: item.priceAtSale,
      productId: item.productId,
      product: {
        id: item.productId,
        name: item.product.name,
        price: item.priceAtSale,
        stock: 0, // Required by the Product type
      },
    })),
  };

  return (
    <DashboardLayout>
      <EnhancedSaleEditPage sale={serializedSale} products={adaptedProducts} />
    </DashboardLayout>
  );
}
