export interface SaleItem {
  id: string;
  quantity: number;
  priceAtSale: number;
  saleId: string;
  productId: string;
  createdAt: Date | string;
  updatedAt: Date | string;
  product: {
    name: string;
  };
}

export interface Sale {
  id: string;
  saleDate: Date | string;
  totalAmount: number;
  transactionNumber?: string | null;
  invoiceRef?: string | null;
  isDraft?: boolean;
  createdAt: Date | string;
  updatedAt: Date | string;
  userId: string;
  user?: {
    id: string;
    name: string | null;
    username: string | null;
  };
  employeeId?: string | null;
  items: SaleItem[];
  customer?: {
    id: string;
    name: string;
  };
  customerId?: string;
  customerEmail?: string;
  paymentMethod?: string;
  status?: string;
  shippingAddress?: string;
  paymentDueDate?: Date | string;
  memo?: string;
  tags?: string[];
  lampiran?: any[];
}

export interface SaleCounts {
  total: number;
  today: number;
  thisMonth: number;
  pending: number;
  drafts: number;
}

export interface ColumnVisibility {
  id: boolean;
  date: boolean;
  paymentDueDate: boolean;
  customer: boolean;
  totalAmount: boolean;
  itemCount: boolean;
  invoiceRef: boolean;
  tags: boolean;
}
