import { db } from "../lib/prisma";
import { createSystemNotification } from "../lib/create-system-notification";

/**
 * This script tests the notification email functionality.
 * Run it with: npx ts-node -r tsconfig-paths/register src/scripts/test-notification-email.ts
 */
async function main() {
  try {
    // Get all users
    const users = await db.user.findMany({
      where: {
        emailVerified: {
          not: null,
        },
      },
      select: {
        id: true,
        name: true,
        email: true,
      },
    });

    if (users.length === 0) {
      console.log("No verified users found. Please verify a user first.");
      return;
    }

    // For each user, create a test notification with email
    for (const user of users) {
      console.log(
        `Creating test notification for user: ${user.name || user.email || user.id}`
      );

      // Create a test notification for each type
      const types = ["info", "warning", "success", "error"] as const;
      
      for (const type of types) {
        // Create notification directly in the database
        await db.notification.create({
          data: {
            userId: user.id,
            type: type.toUpperCase() as any,
            title: `Test ${type} Notification`,
            message: `This is a test ${type} notification created on ${new Date().toLocaleString()}.`,
            isRead: false,
          },
        });
        
        console.log(`Created ${type} notification in database for ${user.email}`);
        
        // Simulate sending email by calling the function directly
        // This bypasses the normal flow but is useful for testing
        try {
          const { sendNotificationEmail } = await import("../lib/send-notification-email");
          const result = await sendNotificationEmail({
            userId: user.id,
            title: `Test ${type} Email Notification`,
            message: `This is a test ${type} email notification sent on ${new Date().toLocaleString()}.`,
            type,
          });
          
          if (result.success) {
            console.log(`Successfully sent ${type} test email to ${user.email}`);
          } else {
            console.error(`Failed to send ${type} test email: ${result.error}`);
          }
        } catch (error) {
          console.error(`Error sending ${type} test email:`, error);
        }
      }
    }

    console.log("Test notifications created successfully!");
  } catch (error) {
    console.error("Error creating test notifications:", error);
  } finally {
    await db.$disconnect();
  }
}

main();
