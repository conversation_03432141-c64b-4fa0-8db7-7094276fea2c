import React from "react";
import { Metadata } from "next";
import DashboardLayout from "@/components/layout/dashboardlayout";
import ContactsPage from "@/components/pages/dashboard/contacts/contacts";
import { auth } from "@/lib/auth";
import { redirect } from "next/navigation";
import { getCustomers } from "@/actions/entities/customers";
import { getSuppliers } from "@/actions/entities/suppliers";

export const metadata: Metadata = {
  title: "Kontak | Kasir Online",
  description: "Kelola data pelanggan dan supplier Anda",
};

const Contacts = async () => {
  const session = await auth();
  if (!session?.user?.id) {
    redirect("/login");
  }

  // Fetch contact data
  const [customersResult, suppliersResult] = await Promise.all([
    getCustomers(),
    getSuppliers(),
  ]);

  if (customersResult.error) {
    console.error("Error fetching customers:", customersResult.error);
  }

  if (suppliersResult.error) {
    console.error("Error fetching suppliers:", suppliersResult.error);
  }

  return (
    <DashboardLayout>
      <ContactsPage
        customers={customersResult.customers || []}
        suppliers={suppliersResult.suppliers || []}
      />
    </DashboardLayout>
  );
};

export default Contacts;
