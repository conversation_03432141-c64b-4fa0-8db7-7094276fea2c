"use client";

import React from "react";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Package } from "lucide-react";
import Image from "next/image";

interface Category {
  id: string;
  name: string;
}

interface ProductSummaryProps {
  name: string;
  image: string | null;
  price: number;
  discountPrice: number | null;
  cost: number | null;
  stock: number;
  minStockLevel?: number;
  category: Category | null;
}

const ProductSummary: React.FC<ProductSummaryProps> = ({
  name,
  image,
  price,
  discountPrice,
  cost,
  stock,
  minStockLevel,
  category,
}) => {
  // Calculate profit if both price and cost are available
  // Use discount price for profit calculation if available
  const effectivePrice = discountPrice || price;
  const profit = effectivePrice && cost ? effectivePrice - cost : null;
  const profitPercentage = profit && cost ? (profit / cost) * 100 : null;

  // Determine stock status
  const getStockStatus = () => {
    if (stock <= 0) return { label: "Habis", color: "destructive" };
    if (minStockLevel && stock <= minStockLevel)
      return { label: "Stok Rendah", color: "warning" };
    return { label: "Tersedia", color: "success" };
  };

  const stockStatus = getStockStatus();

  return (
    <div className="space-y-6">
      {/* Product Image Card */}
      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-lg">Gambar Produk</CardTitle>
        </CardHeader>
        <CardContent>
          {image ? (
            <div className="relative w-full h-64 rounded-md overflow-hidden">
              <Image
                src={image}
                alt={name}
                fill
                style={{ objectFit: "contain" }}
                className="bg-white"
              />
            </div>
          ) : (
            <div className="w-full h-64 border rounded-md flex items-center justify-center bg-gray-100">
              <Package className="h-12 w-12 text-gray-400" />
            </div>
          )}
        </CardContent>
      </Card>

      {/* Summary Card */}
      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-lg">Ringkasan</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Stock Status */}
          <div>
            <p className="text-sm font-medium mb-1">Status Stok</p>
            <Badge
              variant={
                stockStatus.color === "warning"
                  ? "outline"
                  : stockStatus.color === "success"
                  ? "secondary"
                  : "destructive"
              }
            >
              {stockStatus.label}
            </Badge>
          </div>

          {/* Price Info */}
          <div>
            <p className="text-sm font-medium mb-1">Harga Jual</p>
            <p className="text-2xl font-bold">
              Rp {price.toLocaleString("id-ID")}
            </p>
            {discountPrice && (
              <div className="mt-1">
                <p className="text-sm font-medium text-green-600 dark:text-green-400">
                  Harga Diskon
                </p>
                <p className="text-xl font-bold text-green-600 dark:text-green-400">
                  Rp {discountPrice.toLocaleString("id-ID")}
                </p>
              </div>
            )}
          </div>

          {/* Cost and Profit */}
          {cost !== null && (
            <div className="grid grid-cols-2 gap-4">
              <div>
                <p className="text-sm font-medium mb-1">Harga Beli</p>
                <p className="text-lg">Rp {cost.toLocaleString("id-ID")}</p>
              </div>
              {profit !== null && (
                <div>
                  <p className="text-sm font-medium mb-1">Keuntungan</p>
                  <div className="flex items-center gap-2">
                    <p className="text-lg">
                      Rp {profit.toLocaleString("id-ID")}
                    </p>
                    {profitPercentage !== null && (
                      <Badge variant="outline" className="text-green-600">
                        +{profitPercentage.toFixed(1)}%
                      </Badge>
                    )}
                  </div>
                </div>
              )}
            </div>
          )}

          {/* Stock Info */}
          <div className="grid grid-cols-2 gap-4">
            <div>
              <p className="text-sm font-medium mb-1">Stok Saat Ini</p>
              <p className="text-lg">{stock}</p>
            </div>
            {minStockLevel !== undefined && (
              <div>
                <p className="text-sm font-medium mb-1">Stok Minimum</p>
                <p className="text-lg">{minStockLevel}</p>
              </div>
            )}
          </div>

          {/* Category */}
          {category && (
            <div>
              <p className="text-sm font-medium mb-1">Kategori</p>
              <Badge variant="secondary">{category.name}</Badge>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default ProductSummary;
