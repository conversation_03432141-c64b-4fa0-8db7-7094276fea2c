"use client";

import React from "react";
import { SummaryCard, ColorScheme } from "./SummaryCard";
import {
  CurrencyDollarIcon,
  CubeIcon,
  UsersIcon,
  ShoppingBagIcon,
} from "@heroicons/react/24/outline";

// Helper to format currency
export const formatCurrency = (value: number) => {
  return new Intl.NumberFormat("id-ID", {
    style: "currency",
    currency: "IDR",
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(value);
};

// Helper to format percentage change for display
export const formatChange = (change: number) => {
  const sign = change > 0 ? "+" : "";
  return `${sign}${change}%`;
};

export interface SummaryData {
  salesToday: number;
  salesChange: number;
  totalProducts: number;
  newProductsCount: number;
  totalCustomers: number;
  newCustomersCount: number;
  purchasesThisMonth: number;
  purchasesChange: number;
}

interface SummaryCardConfig {
  title: string;
  value: (data: SummaryData) => string;
  icon: React.ReactNode;
  change?: (data: SummaryData) => {
    value: string;
    type: "increase" | "decrease";
  };
  linkText?: string;
  linkHref?: string;
  colorScheme?: ColorScheme;
}

interface SummaryCardsSectionProps {
  summaryData: SummaryData;
  className?: string;
}

export function SummaryCardsSection({
  summaryData,
  className = "",
}: SummaryCardsSectionProps) {
  // Define the cards configuration
  const cardConfigs: SummaryCardConfig[] = [
    {
      title: "Penjualan Hari Ini",
      value: (data) => formatCurrency(data.salesToday),
      icon: <CurrencyDollarIcon className="h-5 w-5" />,
      change: (data) => ({
        value: `${formatChange(data.salesChange)} dari kemarin`,
        type: data.salesChange >= 0 ? "increase" : "decrease",
      }),
      linkText: "Lihat semua penjualan",
      linkHref: "/dashboard/sales",
      colorScheme: "indigo",
    },
    {
      title: "Produk Aktif",
      value: (data) => data.totalProducts.toString(),
      icon: <CubeIcon className="h-5 w-5" />,
      change: (data) => ({
        value: `+${data.newProductsCount} baru (7 hari)`,
        type: "increase",
      }),
      linkText: "Kelola produk",
      linkHref: "/dashboard/products",
      colorScheme: "emerald",
    },
    {
      title: "Pelanggan",
      value: (data) => data.totalCustomers.toString(),
      icon: <UsersIcon className="h-5 w-5" />,
      change: (data) => ({
        value: `+${data.newCustomersCount} baru (7 hari)`,
        type: "increase",
      }),
      linkText: "Lihat pelanggan",
      linkHref: "/dashboard/customers",
      colorScheme: "blue",
    },
    {
      title: "Pembelian Bulan Ini",
      value: (data) => formatCurrency(data.purchasesThisMonth),
      icon: <ShoppingBagIcon className="h-5 w-5" />,
      change: (data) => ({
        value: `${formatChange(data.purchasesChange)} dari bulan lalu`,
        type: data.purchasesChange >= 0 ? "increase" : "decrease",
      }),
      linkText: "Lihat pembelian",
      linkHref: "/dashboard/purchases",
      colorScheme: "amber",
    },
  ];

  return (
    <div className={`grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4 ${className}`}>
      {cardConfigs.map((config, index) => (
        <SummaryCard
          key={index}
          title={config.title}
          value={config.value(summaryData)}
          icon={config.icon}
          change={config.change ? config.change(summaryData) : undefined}
          linkText={config.linkText}
          linkHref={config.linkHref}
          colorScheme={config.colorScheme}
        />
      ))}
    </div>
  );
}
