import React from "react";
import { notFound } from "next/navigation";
import PurchaseDetailPage from "@/components/pages/dashboard/purchases/detail";
import { getPurchaseById } from "@/actions/entities/purchases";
import type { Purchase } from "@/components/pages/dashboard/purchases/types";
import { Metadata } from "next";

export const metadata: Metadata = {
  title: "Detail Pembelian | Kasir Online",
  description: "Lihat detail transaksi pembelian",
};

type PageProps = {
  params: Promise<{ id: string }>;
  searchParams?: Promise<Record<string, string | string[]>>;
};

// This is an async Server Component
export default async function PurchaseDetail(props: PageProps) {
  // Get the id from params (which is a Promise in Next.js 15)
  const params = await props.params;
  const id = params.id as string;

  // Fetch the purchase with the given ID
  const purchaseResult = await getPurchaseById(id);

  // If purchase not found, return 404
  if (!purchaseResult.purchase) {
    notFound();
  }

  // The purchase data is already serialized in getPurchaseById
  // Use type assertion to handle the type mismatch between null and undefined
  return (
    <PurchaseDetailPage
      purchase={purchaseResult.purchase as unknown as Purchase}
    />
  );
}
