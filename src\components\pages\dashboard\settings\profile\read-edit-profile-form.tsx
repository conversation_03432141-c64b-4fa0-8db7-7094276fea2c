"use client";

import { useState } from "react";
import { updateProfile } from "@/actions/users/profile";
import { useSession } from "next-auth/react";
import { User } from "./types";
import {
  UserCircle,
  Mail,
  Phone,
  AtSign,
  AlertCircle,
  CheckCircle,
  Calendar,
  Edit2,
  Save,
  X,
  FileText,
  Lock,
  Building,
} from "lucide-react";
import { DatePicker } from "@/components/ui/date-picker";
import { format } from "date-fns";
import { id } from "date-fns/locale";
import { Button } from "@/components/ui/button";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

interface ReadEditProfileFormProps {
  user: User;
  name: string;
  setName: (name: string) => void;
  username: string;
  setUsername: (username: string) => void;
  imageUrl: string;
  phone: string;
  setPhone: (phone: string) => void;
  bio: string;
  setBio: (bio: string) => void;
  birthday: Date | undefined;
  setBirthday: (date: Date | undefined) => void;
  handleReset: () => void;
}

export default function ReadEditProfileForm({
  user,
  name,
  setName,
  username,
  setUsername,
  imageUrl,
  phone,
  setPhone,
  bio,
  setBio,
  birthday,
  setBirthday,
  handleReset,
}: ReadEditProfileFormProps) {
  const { update } = useSession();
  const [isProfileLoading, setIsProfileLoading] = useState(false);
  const [profileSuccess, setProfileSuccess] = useState<string | null>(null);
  const [profileError, setProfileError] = useState<string | null>(null);
  const [isEditing, setIsEditing] = useState(false);

  // Format date for display
  const formatDate = (date: Date | null | undefined) => {
    if (!date) return "-";
    return format(new Date(date), "d MMMM yyyy", { locale: id });
  };

  // Form submission
  const handleProfileSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setIsProfileLoading(true);
    setProfileSuccess(null);
    setProfileError(null);

    try {
      const result = await updateProfile({
        name,
        username,
        image: imageUrl,
        phone,
        bio,
        birthday: birthday ? format(birthday, "yyyy-MM-dd") : "",
      });

      if (result.error) {
        setProfileError(result.error);
      } else {
        setProfileSuccess("Profil berhasil diperbarui!");
        setIsEditing(false); // Exit edit mode on success
        await update({
          user: {
            name,
            username,
            image: imageUrl,
            phone,
            bio,
            birthday: birthday ? format(birthday, "yyyy-MM-dd") : "",
          },
        });
      }
    } catch (err) {
      setProfileError("Terjadi kesalahan saat memperbarui profil.");
      console.error(err);
    } finally {
      setIsProfileLoading(false);
    }
  };

  // Cancel editing
  const handleCancelEdit = () => {
    handleReset();
    setIsEditing(false);
    setProfileError(null);
    setProfileSuccess(null);
  };

  return (
    <form onSubmit={handleProfileSubmit} className="space-y-6">
      <div className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 overflow-hidden shadow-sm">
        <div className="px-4 py-5 sm:px-6 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center">
          <div>
            <h3 className="text-base font-medium text-gray-900 dark:text-gray-100">
              {isEditing ? "Edit Profil" : "Informasi Profil"}
            </h3>
            <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
              {isEditing
                ? "Perbarui informasi profil Anda"
                : "Detail informasi profil Anda"}
            </p>
          </div>
          {!isEditing && (
            <Button
              type="button"
              onClick={() => setIsEditing(true)}
              className="flex items-center gap-1.5 cursor-pointer"
              variant="outline"
              size="sm"
            >
              <Edit2 className="h-4 w-4" />
              Edit Profil
            </Button>
          )}
        </div>
        <div className="px-4 py-5 sm:p-6 space-y-6">
          {/* Notifications */}
          {profileSuccess && (
            <div className="rounded-lg bg-green-50 dark:bg-green-900/20 p-4 border border-green-100 dark:border-green-800/30 shadow-sm animate-fade-in">
              <div className="flex items-center">
                <CheckCircle className="h-5 w-5 text-green-500 dark:text-green-400" />
                <div className="ml-3">
                  <p className="text-sm font-medium text-green-800 dark:text-green-200">
                    {profileSuccess}
                  </p>
                </div>
              </div>
            </div>
          )}

          {profileError && (
            <div className="rounded-lg bg-red-50 dark:bg-red-900/20 p-4 border border-red-100 dark:border-red-800/30 shadow-sm animate-fade-in">
              <div className="flex items-center">
                <AlertCircle className="h-5 w-5 text-red-500 dark:text-red-400" />
                <div className="ml-3">
                  <p className="text-sm font-medium text-red-800 dark:text-red-200">
                    {profileError}
                  </p>
                </div>
              </div>
            </div>
          )}

          {/* Form Fields */}
          <div className="grid grid-cols-1 gap-y-6 gap-x-6 sm:grid-cols-6">
            {/* Name Field */}
            <div className="sm:col-span-3">
              <label
                htmlFor="name"
                className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1.5"
              >
                Nama Lengkap
              </label>
              {isEditing ? (
                <div className="relative rounded-md shadow-sm">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <UserCircle className="h-5 w-5 text-indigo-500 dark:text-indigo-400" />
                  </div>
                  <input
                    type="text"
                    name="name"
                    id="name"
                    value={name}
                    onChange={(e) => setName(e.target.value)}
                    className="py-3 pl-10 block w-full border-gray-200 dark:border-gray-700 rounded-lg text-sm focus:border-indigo-500 focus:ring-indigo-500 dark:bg-gray-800 dark:text-gray-200 dark:focus:ring-indigo-600 transition-colors duration-200 ease-in-out shadow-sm hover:border-gray-300 dark:hover:border-gray-600"
                    placeholder="Masukkan nama lengkap"
                  />
                </div>
              ) : (
                <div className="flex items-center space-x-3 py-2">
                  <div className="flex-shrink-0">
                    <UserCircle className="h-5 w-5 text-indigo-500 dark:text-indigo-400" />
                  </div>
                  <div className="text-sm text-gray-900 dark:text-gray-100 font-medium">
                    {name || "-"}
                  </div>
                </div>
              )}
            </div>

            {/* Username Field */}
            <div className="sm:col-span-3">
              <label
                htmlFor="username"
                className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1.5"
              >
                Username
              </label>
              {isEditing ? (
                <div className="relative rounded-md shadow-sm">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <AtSign className="h-5 w-5 text-indigo-500 dark:text-indigo-400" />
                  </div>
                  <input
                    type="text"
                    name="username"
                    id="username"
                    value={username}
                    onChange={(e) => setUsername(e.target.value)}
                    className="py-3 pl-10 block w-full border-gray-200 dark:border-gray-700 rounded-lg text-sm focus:border-indigo-500 focus:ring-indigo-500 dark:bg-gray-800 dark:text-gray-200 dark:focus:ring-indigo-600 transition-colors duration-200 ease-in-out shadow-sm hover:border-gray-300 dark:hover:border-gray-600"
                    placeholder="username"
                  />
                </div>
              ) : (
                <div className="flex items-center space-x-3 py-2">
                  <div className="flex-shrink-0">
                    <AtSign className="h-5 w-5 text-indigo-500 dark:text-indigo-400" />
                  </div>
                  <div className="text-sm text-gray-900 dark:text-gray-100 font-medium">
                    {username ? `@${username}` : "-"}
                  </div>
                </div>
              )}
            </div>

            {/* Email Field - Always Read Only */}
            <div className="sm:col-span-3">
              <label
                htmlFor="email"
                className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1.5"
              >
                Email
              </label>
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <div className="flex items-center space-x-3 py-2 cursor-help">
                      <div className="flex-shrink-0">
                        <Mail className="h-5 w-5 text-gray-400 dark:text-gray-500" />
                      </div>
                      <div className="text-sm text-gray-900 dark:text-gray-100 font-medium">
                        {user.email || "-"}
                      </div>
                      <Lock className="h-4 w-4 text-gray-400 dark:text-gray-500" />
                    </div>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p className="text-xs">Email tidak dapat diubah</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div>

            {/* Company ID Field - Always Read Only */}
            <div className="sm:col-span-3">
              <label
                htmlFor="companyId"
                className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1.5"
              >
                ID Perusahaan
              </label>
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <div className="flex items-center space-x-3 py-2 cursor-help">
                      <div className="flex-shrink-0">
                        <Building className="h-5 w-5 text-gray-400 dark:text-gray-500" />
                      </div>
                      <div className="text-sm text-gray-900 dark:text-gray-100 font-medium">
                        {user.companyId || "Belum tersedia"}
                      </div>
                      <Lock className="h-4 w-4 text-gray-400 dark:text-gray-500" />
                    </div>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p className="text-xs">ID Perusahaan tidak dapat diubah</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div>

            {/* Phone Field */}
            <div className="sm:col-span-3">
              <label
                htmlFor="phone"
                className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1.5"
              >
                Nomor Telepon
              </label>
              {isEditing ? (
                <div className="relative rounded-md shadow-sm">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Phone className="h-5 w-5 text-indigo-500 dark:text-indigo-400" />
                  </div>
                  <input
                    type="tel"
                    name="phone"
                    id="phone"
                    value={phone}
                    onChange={(e) => setPhone(e.target.value)}
                    className="py-3 pl-10 block w-full border-gray-200 dark:border-gray-700 rounded-lg text-sm focus:border-indigo-500 focus:ring-indigo-500 dark:bg-gray-800 dark:text-gray-200 dark:focus:ring-indigo-600 transition-colors duration-200 ease-in-out shadow-sm hover:border-gray-300 dark:hover:border-gray-600"
                    placeholder="+62 812 3456 7890"
                  />
                </div>
              ) : (
                <div className="flex items-center space-x-3 py-2">
                  <div className="flex-shrink-0">
                    <Phone className="h-5 w-5 text-indigo-500 dark:text-indigo-400" />
                  </div>
                  <div className="text-sm text-gray-900 dark:text-gray-100 font-medium">
                    {phone || "-"}
                  </div>
                </div>
              )}
            </div>

            {/* Birthday Field */}
            <div className="sm:col-span-3">
              <label
                htmlFor="birthday"
                className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1.5"
              >
                Tanggal Lahir
              </label>
              {isEditing ? (
                <DatePicker
                  date={birthday}
                  setDate={setBirthday}
                  placeholder="Pilih tanggal lahir"
                  className="py-2.5 pl-3 border-gray-200 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-200 dark:hover:bg-gray-700/50 cursor-pointer shadow-sm hover:border-gray-300 dark:hover:border-gray-600"
                  fromYear={1940}
                  toYear={new Date().getFullYear()}
                />
              ) : (
                <div className="flex items-center space-x-3 py-2">
                  <div className="flex-shrink-0">
                    <Calendar className="h-5 w-5 text-indigo-500 dark:text-indigo-400" />
                  </div>
                  <div className="text-sm text-gray-900 dark:text-gray-100 font-medium">
                    {birthday ? formatDate(birthday) : "-"}
                  </div>
                </div>
              )}
            </div>

            {/* Bio Field */}
            <div className="sm:col-span-6">
              <label
                htmlFor="bio"
                className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1.5"
              >
                Bio
              </label>
              {isEditing ? (
                <div className="relative rounded-md shadow-sm">
                  <textarea
                    id="bio"
                    name="bio"
                    rows={4}
                    value={bio}
                    onChange={(e) => setBio(e.target.value)}
                    className="py-3 px-4 block w-full border-gray-200 dark:border-gray-700 rounded-lg text-sm focus:border-indigo-500 focus:ring-indigo-500 dark:bg-gray-800 dark:text-gray-200 dark:focus:ring-indigo-600 transition-colors duration-200 ease-in-out shadow-sm hover:border-gray-300 dark:hover:border-gray-600"
                    placeholder="Ceritakan sedikit tentang diri Anda..."
                  />
                </div>
              ) : (
                <div className="flex space-x-3 py-2">
                  <div className="flex-shrink-0 pt-1">
                    <FileText className="h-5 w-5 text-indigo-500 dark:text-indigo-400" />
                  </div>
                  <div className="text-sm text-gray-700 dark:text-gray-300">
                    {bio || "-"}
                  </div>
                </div>
              )}
              {isEditing && (
                <p className="mt-2 text-xs text-gray-500 dark:text-gray-400 flex items-center">
                  <span className="inline-block mr-1">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-4 w-4"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                      />
                    </svg>
                  </span>
                  Deskripsi singkat tentang diri Anda. Maksimal 500 karakter.
                </p>
              )}
            </div>
          </div>
        </div>
        {isEditing && (
          <div className="px-6 py-4 bg-gray-50 dark:bg-gray-800/50 flex justify-end gap-3 sm:px-6 border-t border-gray-200 dark:border-gray-700">
            <Button
              type="button"
              onClick={handleCancelEdit}
              variant="outline"
              className="cursor-pointer"
            >
              <X className="h-4 w-4 mr-2" />
              Batal
            </Button>
            <Button
              type="submit"
              disabled={isProfileLoading}
              className="cursor-pointer"
            >
              {isProfileLoading ? (
                <>
                  <svg
                    className="animate-spin -ml-1 mr-2 h-4 w-4 text-white"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                  >
                    <circle
                      className="opacity-25"
                      cx="12"
                      cy="12"
                      r="10"
                      stroke="currentColor"
                      strokeWidth="4"
                    ></circle>
                    <path
                      className="opacity-75"
                      fill="currentColor"
                      d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                    ></path>
                  </svg>
                  Menyimpan...
                </>
              ) : (
                <>
                  <Save className="h-4 w-4 mr-2" />
                  Simpan Perubahan
                </>
              )}
            </Button>
          </div>
        )}
      </div>
    </form>
  );
}
