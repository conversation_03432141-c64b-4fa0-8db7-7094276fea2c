import React, { useEffect, useState } from "react";
import { Control } from "react-hook-form";
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  FormDescription,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { DatePicker } from "@/components/ui/date-picker";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  PurchaseFormValues,
  Supplier,
} from "@/components/pages/dashboard/purchases/new/types"; // Use alias for types
import { getNextTransactionNumber } from "@/actions/entities/purchases";
import {
  Calendar,
  Mail,
  Tag,
  Home,
  Warehouse,
  Hash,
  FileText,
  Building,
  X,
  Plus,
  Settings,
  Loader2,
  ChevronsUpDown,
} from "lucide-react";
import {
  Command,
  CommandGroup,
  CommandInput,
  CommandList,
} from "@/components/ui/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";

interface PurchaseInfoSectionProps {
  control: Control<PurchaseFormValues>;
  isPending: boolean;
  suppliers: Supplier[];
  createdAt?: string; // Optional createdAt for edit mode
}

const PurchaseInfoSection: React.FC<PurchaseInfoSectionProps> = ({
  control,
  isPending,
  suppliers,
  createdAt, // Add createdAt parameter
}) => {
  // State for auto-generation settings
  const [autoTransactionNumber, setAutoTransactionNumber] =
    React.useState(false);
  const [autoInvoiceRef, setAutoInvoiceRef] = React.useState(false);
  const [tagInput, setTagInput] = React.useState("");

  // State for actual next transaction numbers
  const [nextTrxNumber, setNextTrxNumber] = React.useState<string>("");
  const [nextInvNumber, setNextInvNumber] = React.useState<string>("");
  const [isLoadingTrx, setIsLoadingTrx] = React.useState(false);
  const [isLoadingInv, setIsLoadingInv] = React.useState(false);

  // State for supplier dropdown
  const [supplierSearchTerm, setSupplierSearchTerm] = useState("");
  const [supplierDropdownOpen, setSupplierDropdownOpen] = useState(false);

  // Enhanced filtering - search across supplier name
  const filteredSuppliers = suppliers.filter((supplier) => {
    const searchLower = supplierSearchTerm.toLowerCase().trim();
    if (!searchLower) return true;

    return supplier.name.toLowerCase().includes(searchLower);
  });

  // Fetch next transaction number when auto-generation is toggled
  useEffect(() => {
    if (autoTransactionNumber && !nextTrxNumber) {
      setIsLoadingTrx(true);
      // Use createdAt date if available (edit mode), otherwise use current date
      getNextTransactionNumber("TRX", createdAt)
        .then((number) => {
          setNextTrxNumber(number);
        })
        .catch((error) => {
          console.error("Error fetching next transaction number:", error);
        })
        .finally(() => {
          setIsLoadingTrx(false);
        });
    }
  }, [autoTransactionNumber, nextTrxNumber, createdAt]);

  // Fetch next invoice number when auto-generation is toggled
  useEffect(() => {
    if (autoInvoiceRef && !nextInvNumber) {
      setIsLoadingInv(true);
      // Use createdAt date if available (edit mode), otherwise use current date
      getNextTransactionNumber("INV", createdAt)
        .then((number) => {
          setNextInvNumber(number);
        })
        .catch((error) => {
          console.error("Error fetching next invoice number:", error);
        })
        .finally(() => {
          setIsLoadingInv(false);
        });
    }
  }, [autoInvoiceRef, nextInvNumber, createdAt]);

  // Generate fallback example transaction numbers for placeholders
  const currentDate = new Date();
  const year = currentDate.getFullYear();
  const fallbackTrxNumber = `TRX-${year}-B000001`;
  const fallbackInvNumber = `INV-${year}-B000001`;
  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
        {/* Supplier Selection */}
        <FormField
          control={control}
          name="supplierId"
          render={({ field }) => (
            <FormItem className="col-span-1">
              <FormLabel className="flex items-center gap-1.5">
                <Building className="h-4 w-4 text-indigo-600" />
                Supplier *
              </FormLabel>
              <Popover
                open={supplierDropdownOpen}
                onOpenChange={setSupplierDropdownOpen}
              >
                <PopoverTrigger asChild>
                  <FormControl>
                    <Button
                      variant="outline"
                      role="combobox"
                      aria-expanded={supplierDropdownOpen}
                      className={`w-full justify-between hover:bg-accent hover:text-accent-foreground ${
                        !field.value ? "text-muted-foreground" : ""
                      }`}
                      disabled={isPending}
                    >
                      {suppliers.length === 0 ? (
                        <div className="flex items-center">
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          Memuat supplier...
                        </div>
                      ) : field.value ? (
                        <div className="flex items-center">
                          <Building className="mr-2 h-4 w-4" />
                          <span>
                            {suppliers.find(
                              (supplier) => supplier.id === field.value
                            )?.name || "Pilih supplier"}
                          </span>
                        </div>
                      ) : (
                        <div className="flex items-center">
                          <Building className="mr-2 h-4 w-4" />
                          <span>Pilih supplier</span>
                        </div>
                      )}
                      <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                    </Button>
                  </FormControl>
                </PopoverTrigger>
                <PopoverContent className="w-[var(--radix-popover-trigger-width)] p-0">
                  <Command shouldFilter={false}>
                    <CommandInput
                      placeholder="Cari supplier..."
                      value={supplierSearchTerm}
                      onValueChange={setSupplierSearchTerm}
                      className="h-9"
                      onFocus={() => {
                        if (!supplierDropdownOpen) {
                          setSupplierDropdownOpen(true);
                        }
                      }}
                    />
                    <CommandList className="max-h-[300px] overflow-y-auto">
                      <CommandGroup>
                        {/* Show all filtered suppliers or just 3 if not searching */}
                        {(supplierSearchTerm
                          ? filteredSuppliers
                          : suppliers.slice(0, 3)
                        ).map((supplier) => (
                          <div key={supplier.id} className="px-2">
                            <button
                              type="button"
                              className="w-full flex items-center text-left rounded-md px-3 py-3 text-sm bg-transparent hover:bg-accent hover:text-accent-foreground cursor-pointer transition-colors border border-transparent hover:border-gray-200 focus:bg-accent focus:text-accent-foreground focus:outline-none"
                              onClick={() => {
                                field.onChange(supplier.id);
                                setSupplierDropdownOpen(false);
                                setSupplierSearchTerm("");

                                // Update email field if available
                                const emailField = document.querySelector(
                                  'input[name="supplierEmail"]'
                                ) as HTMLInputElement;

                                if (emailField) {
                                  emailField.value = "";
                                  // Trigger a change event to update the form
                                  const event = new Event("input", {
                                    bubbles: true,
                                  });
                                  emailField.dispatchEvent(event);
                                }
                              }}
                            >
                              <Building className="mr-2 h-4 w-4 shrink-0 text-indigo-500" />
                              <div className="flex flex-col flex-grow">
                                <span className="font-medium">
                                  {supplier.name}
                                </span>
                              </div>
                              <span className="text-xs text-indigo-500 ml-2">
                                Pilih
                              </span>
                            </button>
                          </div>
                        ))}
                      </CommandGroup>
                    </CommandList>
                  </Command>
                </PopoverContent>
              </Popover>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Supplier Email */}
        <FormField
          control={control}
          name="supplierEmail"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="flex items-center gap-1.5">
                <Mail className="h-4 w-4 text-blue-600" />
                Email Supplier (Opsional)
              </FormLabel>
              <FormControl>
                <Input
                  type="email"
                  placeholder="<EMAIL>"
                  {...field}
                  disabled={isPending}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Transaction Date */}
        <FormField
          control={control}
          name="transactionDate"
          render={({ field: { value, onChange } }) => (
            <FormItem>
              <FormLabel className="flex items-center gap-1.5">
                <Calendar className="h-4 w-4 text-green-600" />
                Tgl. Transaksi
              </FormLabel>
              <FormControl>
                <DatePicker
                  date={value ? new Date(value) : new Date()}
                  setDate={(date) => onChange(date || new Date())}
                  placeholder="Pilih tanggal transaksi"
                  disabled={isPending}
                  className="w-full"
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Payment Due Date */}
        <FormField
          control={control}
          name="paymentDueDate"
          render={({ field: { value, onChange } }) => (
            <FormItem>
              <FormLabel className="flex items-center gap-1.5">
                <Calendar className="h-4 w-4 text-red-600" />
                Tgl. Jatuh Tempo
              </FormLabel>
              <FormControl>
                <DatePicker
                  date={value ? new Date(value) : undefined}
                  setDate={onChange}
                  placeholder="Pilih tanggal jatuh tempo"
                  disabled={isPending}
                  className="w-full"
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Transaction Number */}
        <FormField
          control={control}
          name="transactionNumber"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="flex items-center gap-1.5">
                <Hash className="h-4 w-4 text-purple-600" />
                Nomor Transaksi
              </FormLabel>
              <div className="relative">
                <FormControl>
                  <Input
                    placeholder={
                      autoTransactionNumber
                        ? isLoadingTrx
                          ? "Loading..."
                          : `Auto - ${nextTrxNumber || fallbackTrxNumber}`
                        : `Contoh: TRX-${year}-B000001`
                    }
                    {...field}
                    disabled={isPending || autoTransactionNumber}
                    value={autoTransactionNumber ? "" : field.value}
                    onChange={(e) => {
                      if (!autoTransactionNumber) {
                        field.onChange(e.target.value);
                      }
                    }}
                  />
                </FormControl>
                {isLoadingTrx && autoTransactionNumber ? (
                  <div className="absolute right-2 top-1/2 -translate-y-1/2 text-gray-500">
                    <Loader2 className="h-4 w-4 animate-spin" />
                  </div>
                ) : (
                  <button
                    type="button"
                    className="absolute right-2 top-1/2 -translate-y-1/2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 cursor-pointer"
                    onClick={() => {
                      setAutoTransactionNumber(!autoTransactionNumber);
                      if (!autoTransactionNumber) {
                        // When enabling auto, clear the field
                        field.onChange("");
                      }
                    }}
                    title={
                      autoTransactionNumber
                        ? "Disable auto-generation"
                        : "Enable auto-generation"
                    }
                  >
                    <Settings className="h-4 w-4" />
                  </button>
                )}
              </div>
              {autoTransactionNumber && (
                <p className="text-xs text-muted-foreground mt-1">
                  Nomor transaksi akan digenerate otomatis
                </p>
              )}
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Invoice Reference */}
        <FormField
          control={control}
          name="invoiceRef"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="flex items-center gap-1.5">
                <FileText className="h-4 w-4 text-yellow-600" />
                Nomor Invoice
              </FormLabel>
              <div className="relative">
                <FormControl>
                  <Input
                    placeholder={
                      autoInvoiceRef
                        ? isLoadingInv
                          ? "Loading..."
                          : `Auto - ${nextInvNumber || fallbackInvNumber}`
                        : `Contoh: INV-${year}-B000001`
                    }
                    {...field}
                    disabled={isPending || autoInvoiceRef}
                    value={autoInvoiceRef ? "" : field.value}
                    onChange={(e) => {
                      if (!autoInvoiceRef) {
                        field.onChange(e.target.value);
                      }
                    }}
                  />
                </FormControl>
                {isLoadingInv && autoInvoiceRef ? (
                  <div className="absolute right-2 top-1/2 -translate-y-1/2 text-gray-500">
                    <Loader2 className="h-4 w-4 animate-spin" />
                  </div>
                ) : (
                  <button
                    type="button"
                    className="absolute right-2 top-1/2 -translate-y-1/2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 cursor-pointer"
                    onClick={() => {
                      setAutoInvoiceRef(!autoInvoiceRef);
                      if (!autoInvoiceRef) {
                        // When enabling auto, clear the field
                        field.onChange("");
                      }
                    }}
                    title={
                      autoInvoiceRef
                        ? "Disable auto-generation"
                        : "Enable auto-generation"
                    }
                  >
                    <Settings className="h-4 w-4" />
                  </button>
                )}
              </div>
              {autoInvoiceRef && (
                <p className="text-xs text-muted-foreground mt-1">
                  Nomor invoice akan digenerate otomatis
                </p>
              )}
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Warehouse */}
        <FormField
          control={control}
          name="warehouse"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="flex items-center gap-1.5">
                <Warehouse className="h-4 w-4 text-orange-600" />
                Gudang
              </FormLabel>
              <FormControl>
                <Input
                  placeholder="Gudang Utama"
                  {...field}
                  disabled={isPending}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Tags */}
        <FormField
          control={control}
          name="tags"
          render={({ field }) => {
            const handleAddTag = () => {
              if (tagInput.trim()) {
                // Check if the tag already exists
                if (!field.value?.includes(tagInput.trim())) {
                  const newTags = [...(field.value || []), tagInput.trim()];
                  field.onChange(newTags);
                }
                setTagInput("");
              }
            };

            const handleKeyDown = (e: React.KeyboardEvent) => {
              if (e.key === "Enter" || e.key === ",") {
                e.preventDefault();

                // If comma is pressed, we might have multiple tags
                if (e.key === ",") {
                  const tags = tagInput
                    .split(",")
                    .map((tag) => tag.trim())
                    .filter((tag) => tag !== "");

                  if (tags.length > 0) {
                    // Add all tags that don't already exist
                    const existingTags = field.value || [];
                    const newTags = [...existingTags];

                    tags.forEach((tag) => {
                      if (!existingTags.includes(tag)) {
                        newTags.push(tag);
                      }
                    });

                    field.onChange(newTags);
                    setTagInput("");
                    return;
                  }
                }

                handleAddTag();
              }
            };

            return (
              <FormItem>
                <FormLabel className="flex items-center gap-1.5">
                  <Tag className="h-4 w-4 text-teal-600" />
                  Tag
                </FormLabel>

                {/* Input for new tags - Moved to top */}
                <div className="flex gap-2 mb-3">
                  <FormControl>
                    <Input
                      placeholder="Tambahkan tag (tekan Enter atau koma)"
                      value={tagInput}
                      onChange={(e) => setTagInput(e.target.value)}
                      onKeyDown={handleKeyDown}
                      disabled={isPending}
                      className="flex-1"
                    />
                  </FormControl>
                  <Button
                    type="button"
                    size="sm"
                    variant="outline"
                    onClick={handleAddTag}
                    disabled={isPending || !tagInput.trim()}
                    className="border-teal-200 hover:bg-teal-100 hover:text-teal-800 cursor-pointer"
                  >
                    <Plus className="h-4 w-4 mr-1" />
                    Tambah
                  </Button>
                </div>

                {/* Display existing tags - Moved to bottom */}
                <div className="flex flex-wrap gap-2">
                  {field.value && field.value.length > 0 ? (
                    field.value.map((tag, index) => (
                      <Badge
                        key={index}
                        variant="secondary"
                        className="bg-teal-100 text-teal-800 dark:bg-teal-900/30 dark:text-teal-300 flex items-center gap-1 shadow-sm"
                      >
                        {tag}
                        <button
                          type="button"
                          className="ml-1 hover:text-red-500 cursor-pointer"
                          onClick={() => {
                            const newTags = [...(field.value || [])];
                            newTags.splice(index, 1);
                            field.onChange(newTags);
                          }}
                          disabled={isPending}
                        >
                          <X className="h-3 w-3" />
                        </button>
                      </Badge>
                    ))
                  ) : (
                    <span className="text-sm text-muted-foreground">
                      Belum ada tag
                    </span>
                  )}
                </div>

                <FormDescription>
                  Tag untuk memudahkan pencarian dan pengelompokan
                </FormDescription>
                <FormMessage />
              </FormItem>
            );
          }}
        />
      </div>

      {/* Billing Address */}
      <FormField
        control={control}
        name="billingAddress"
        render={({ field }) => (
          <FormItem>
            <FormLabel className="flex items-center gap-1.5">
              <Home className="h-4 w-4 text-indigo-600" />
              Alamat Penagihan
            </FormLabel>
            <FormControl>
              <Textarea
                placeholder="Masukkan alamat lengkap untuk penagihan"
                className="resize-y min-h-[80px]"
                {...field}
                disabled={isPending}
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
    </div>
  );
};

export default PurchaseInfoSection;
