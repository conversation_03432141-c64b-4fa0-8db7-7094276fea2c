"use client";

import React, { useState, useEffect, useTransition } from "react";
import { useRouter } from "next/navigation";
import { useForm, useFieldArray } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { toast } from "sonner";
import { EnhancedPurchaseSchema } from "./new/types";
import { addPurchase } from "@/actions/entities/purchases";
import { Form } from "@/components/ui/form";
import { Card, CardContent } from "@/components/ui/card";
import {
  PurchaseFormValues,
  Product,
  Supplier,
} from "@/components/pages/dashboard/purchases/new/types"; // Use alias
import PurchaseFormHeader from "@/components/pages/dashboard/purchases/new/components/PurchaseFormHeader"; // Use alias
import PurchaseInfoSection from "@/components/pages/dashboard/purchases/new/components/PurchaseInfoSection"; // Use alias
import PurchaseItemTable from "@/components/pages/dashboard/purchases/new/components/PurchaseItemTable"; // Use alias
import PurchaseTotalSection from "@/components/pages/dashboard/purchases/new/components/PurchaseTotalSection"; // Use alias
import PurchaseFormActions from "@/components/pages/dashboard/purchases/new/components/PurchaseFormActions"; // Use alias
import AdditionalInfo from "@/components/pages/dashboard/purchases/new/components/AdditionalInfo"; // Import AdditionalInfo component

// Props remain the same, but types are imported
interface NewPurchasePageProps {
  products: Product[];
  suppliers: Supplier[];
}

const NewPurchasePage: React.FC<NewPurchasePageProps> = ({
  products,
  suppliers,
}) => {
  const router = useRouter();
  const [isPending, startTransition] = useTransition();
  const [totalAmount, setTotalAmount] = useState<number>(0);

  // Initialize the form
  const form = useForm<PurchaseFormValues>({
    resolver: zodResolver(EnhancedPurchaseSchema),
    defaultValues: {
      items: [
        {
          productId: "",
          quantity: 1,
          costAtPurchase: 0,
          unit: "Buah",
          tax: "",
        },
      ],
      totalAmount: 0,
      invoiceRef: "",
      supplierId: "",
      paymentStatus: "paid",
      trackDelivery: false,
      notifyOnArrival: false,
      // New fields
      supplierEmail: "",
      transactionDate: new Date(),
      transactionNumber: "",
      warehouse: "",
      tags: [],
      billingAddress: "",
      isDraft: false,
      priceIncludesTax: false,
      // Additional info fields
      memo: "",
      lampiran: [],
    },
  });

  // Get the items field array - keep control for passing down
  const { fields, append, remove } = useFieldArray({
    control: form.control, // Keep control
    name: "items",
  });

  // Watch for changes in the items array to calculate the total
  const items = form.watch("items");

  // Calculate total amount whenever items change
  useEffect(() => {
    const priceIncludesTax = form.watch("priceIncludesTax");

    const total = items.reduce(
      (sum: number, item: PurchaseFormValues["items"][number]) => {
        // Ensure item and properties exist before calculation
        const quantity = item?.quantity ?? 0;
        const cost = item?.costAtPurchase ?? 0;
        const taxRate = parseFloat(item?.tax || "0") / 100;

        let itemSubtotal;
        if (priceIncludesTax) {
          // If price includes tax, the total is simply quantity * cost
          itemSubtotal = quantity * cost;
        } else {
          // If price doesn't include tax, we add tax to the price
          const subtotalBeforeTax = quantity * cost;
          const taxAmount = subtotalBeforeTax * taxRate;
          itemSubtotal = subtotalBeforeTax + taxAmount;
        }

        return sum + itemSubtotal;
      },
      0
    );

    setTotalAmount(total);
    form.setValue("totalAmount", total);
  }, [items, form]);

  // Handle product selection
  const handleProductChange = (index: number, productId: string) => {
    const selectedProduct: Product | undefined = products.find(
      (p) => p.id === productId
    );
    // Check if selectedProduct exists and its cost is a number
    if (selectedProduct && typeof selectedProduct.cost === "number") {
      const costValue = selectedProduct.cost; // Assign to variable to help TS inference
      form.setValue(`items.${index}.costAtPurchase`, costValue);

      // Force recalculation of total immediately
      const currentItems = form.getValues("items");

      // Ensure the item exists before trying to update it
      if (currentItems[index]) {
        currentItems[index].costAtPurchase = selectedProduct.cost;
      }

      const priceIncludesTax = form.watch("priceIncludesTax");

      const total = currentItems.reduce(
        (sum: number, item: PurchaseFormValues["items"][number]) => {
          // Ensure item and properties exist before calculation
          const quantity = item?.quantity ?? 0;
          const cost = item?.costAtPurchase ?? 0;
          const taxRate = parseFloat(item?.tax || "0") / 100;

          let itemSubtotal;
          if (priceIncludesTax) {
            // If price includes tax, the total is simply quantity * cost
            itemSubtotal = quantity * cost;
          } else {
            // If price doesn't include tax, we add tax to the price
            const subtotalBeforeTax = quantity * cost;
            const taxAmount = subtotalBeforeTax * taxRate;
            itemSubtotal = subtotalBeforeTax + taxAmount;
          }

          return sum + itemSubtotal;
        },
        0
      );

      setTotalAmount(total);
      form.setValue("totalAmount", total);
    }
  };

  // Handle form submission
  const onSubmit = (values: PurchaseFormValues) => {
    startTransition(async () => {
      try {
        // Include all fields from the form
        const purchaseData = {
          items: values.items,
          totalAmount: values.totalAmount,
          invoiceRef: values.invoiceRef,
          supplierId: values.supplierId,
          isDraft: false, // Not a draft when submitting normally
          // Include additional fields
          supplierEmail: values.supplierEmail || "",
          transactionDate: values.transactionDate || new Date(),
          paymentDueDate: values.paymentDueDate,
          transactionNumber: values.transactionNumber,
          tags: values.tags || [],
          billingAddress: values.billingAddress || "",
          warehouse: values.warehouse || "",
          priceIncludesTax: values.priceIncludesTax || false,
          // Include form fields for additional info
          memo: values.memo || "",
          lampiran: values.lampiran || [],
        };

        const result = await addPurchase(purchaseData);
        if (result.success) {
          toast.success(result.success);
          form.reset(); // Reset form on success
          // Redirect after a short delay
          router.push("/dashboard/purchases");
        } else if (result.error) {
          toast.error(result.error);
        } else {
          toast.error("Terjadi kesalahan yang tidak diketahui.");
        }
      } catch (error) {
        console.error("Submit Error:", error);
        toast.error("Gagal menghubungi server.");
      }
    });
  };

  return (
    <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
      <Card>
        <PurchaseFormHeader />
        <CardContent>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              {/* Form Content */}
              <PurchaseInfoSection
                control={form.control}
                isPending={isPending}
                suppliers={suppliers}
              />

              <div className="mt-6">
                <PurchaseItemTable
                  control={form.control}
                  isPending={isPending}
                  products={products}
                  items={items}
                  fields={fields}
                  append={append}
                  remove={remove}
                  handleProductChange={handleProductChange}
                />
              </div>

              <PurchaseTotalSection totalAmount={totalAmount} />

              {/* Additional Info Section */}
              <div className="border-t border-gray-200 dark:border-gray-700 pt-6 mt-6">
                <h3 className="text-lg font-medium mb-4">Informasi Tambahan</h3>
                <AdditionalInfo
                  control={form.control}
                  isPending={isPending}
                  items={items}
                />
              </div>

              {/* Hidden total amount field (still needed for form submission) */}
              <input
                type="hidden"
                {...form.register("totalAmount")}
                value={totalAmount}
              />

              <PurchaseFormActions
                isPending={isPending}
                onCancel={() => router.back()}
              />
            </form>
          </Form>
        </CardContent>
      </Card>
    </div>
  );
};

export default NewPurchasePage;
