"use client";

import React, { useState } from "react";
import { Control, useFieldArray } from "react-hook-form";
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { ProductFormValues, ColorVariant } from "../types";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { Check, Circle, Edit2, Plus, Trash2, X } from "lucide-react";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";

interface ProductColorVariantsProps {
  control: Control<ProductFormValues>;
  isPending: boolean;
}

const ProductColorVariants: React.FC<ProductColorVariantsProps> = ({
  control,
  isPending,
}) => {
  const [showVariantForm, setShowVariantForm] = useState(false);
  const [editingIndex, setEditingIndex] = useState<number | null>(null);
  const [newVariant, setNewVariant] = useState<Partial<ColorVariant>>({
    colorName: "",
    colorCode: "#000000",
    stock: 0,
    price: undefined,
  });

  // Use useFieldArray to manage the variants array
  const { fields, append, update, remove } = useFieldArray({
    control,
    name: "colorVariants",
  });

  // Get the hasVariants value from the form
  const hasVariantsField = control._formValues.hasVariants;

  // Handle adding or updating a variant
  const handleSaveVariant = () => {
    if (!newVariant.colorName || !newVariant.colorCode) {
      return;
    }

    const variantData = {
      id: newVariant.id,
      sku: newVariant.sku || "",
      colorName: newVariant.colorName,
      colorCode: newVariant.colorCode,
      price: newVariant.price || undefined,
      stock: newVariant.stock || 0,
      image: newVariant.image || "",
    };

    if (editingIndex !== null) {
      // Update existing variant
      update(editingIndex, variantData);
    } else {
      // Add new variant
      append(variantData);
    }

    // Reset the form
    resetVariantForm();
  };

  // Handle editing a variant
  const handleEditVariant = (index: number) => {
    const variant = fields[index];
    setNewVariant({
      id: variant.id,
      colorName: variant.colorName,
      colorCode: variant.colorCode,
      sku: variant.sku,
      stock: variant.stock,
      price: variant.price,
      image: variant.image,
    });
    setEditingIndex(index);
    setShowVariantForm(true);
  };

  // Reset the variant form
  const resetVariantForm = () => {
    setNewVariant({
      colorName: "",
      colorCode: "#000000",
      stock: 0,
      price: undefined,
    });
    setEditingIndex(null);
    setShowVariantForm(false);
  };

  // Handle canceling the variant form
  const handleCancelVariant = () => {
    resetVariantForm();
  };

  return (
    <div className="space-y-8">
      <Accordion type="single" collapsible className="w-full">
        <AccordionItem
          value="color-variants"
          className="border rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-shadow duration-200"
        >
          <AccordionTrigger className="py-3 px-4 hover:no-underline bg-gray-50 dark:bg-gray-800 hover:bg-gray-100 dark:hover:bg-gray-700 transition-all duration-300 cursor-pointer group">
            <h3 className="text-base font-medium group-hover:translate-x-0.5 transition-transform duration-300 flex items-center gap-2">
              <Circle className="h-5 w-5 text-blue-500 fill-blue-500 stroke-blue-500" />
              Varian Warna Produk
            </h3>
          </AccordionTrigger>
          <AccordionContent className="px-4">
            <div className="space-y-4 pt-4">
              {/* Toggle for enabling color variants */}
              <div className="flex items-center space-x-3">
                <FormField
                  control={control}
                  name="hasVariants"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center space-x-2 space-y-0">
                      <FormControl>
                        <Switch
                          checked={field.value}
                          onCheckedChange={field.onChange}
                          disabled={isPending}
                        />
                      </FormControl>
                      <FormLabel className="font-normal">
                        {field.value
                          ? "Produk memiliki varian warna"
                          : "Produk tidak memiliki varian warna"}
                      </FormLabel>
                    </FormItem>
                  )}
                />
              </div>

              {/* Variant Management Section */}
              {hasVariantsField && (
                <div className="space-y-4">
                  <Separator />

                  {/* Existing Variants */}
                  {fields.length > 0 && (
                    <div>
                      <h4 className="text-sm font-medium mb-2">
                        Varian Warna yang Tersedia
                      </h4>
                      <div className="space-y-2">
                        {fields.map((field, index) => (
                          <div
                            key={field.id}
                            className="flex items-center justify-between p-2 bg-muted/50 rounded-md border"
                          >
                            <div className="flex items-center space-x-2">
                              <div
                                className="w-5 h-5 rounded-full border"
                                style={{ backgroundColor: field.colorCode }}
                              />
                              <div>
                                <p className="text-sm">{field.colorName}</p>
                                <p className="text-xs text-muted-foreground">
                                  Stok: {field.stock}{" "}
                                  {field.price &&
                                    `• Harga: Rp ${field.price.toLocaleString("id-ID")}`}
                                </p>
                              </div>
                            </div>
                            <div className="flex space-x-1">
                              <Button
                                type="button"
                                variant="ghost"
                                size="icon"
                                className="h-8 w-8 cursor-pointer"
                                onClick={() => handleEditVariant(index)}
                                disabled={isPending}
                              >
                                <Edit2 className="h-4 w-4" />
                              </Button>
                              <Button
                                type="button"
                                variant="ghost"
                                size="icon"
                                className="h-8 w-8 cursor-pointer"
                                onClick={() => remove(index)}
                                disabled={isPending}
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Add New Variant Button or Form */}
                  {!showVariantForm ? (
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      className="w-full cursor-pointer"
                      onClick={() => setShowVariantForm(true)}
                      disabled={isPending}
                    >
                      <Plus className="h-4 w-4 mr-2" />
                      Tambah Varian Warna
                    </Button>
                  ) : (
                    <div className="p-3 border rounded-md bg-muted/50">
                      <h4 className="text-sm font-medium mb-3">
                        {editingIndex !== null
                          ? "Edit Varian Warna"
                          : "Tambah Varian Warna Baru"}
                      </h4>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-3 mb-3">
                        <div className="space-y-2">
                          <Label htmlFor="colorName">Nama Warna</Label>
                          <Input
                            id="colorName"
                            placeholder="Contoh: Merah, Biru, Hitam"
                            value={newVariant.colorName || ""}
                            onChange={(e) =>
                              setNewVariant({
                                ...newVariant,
                                colorName: e.target.value,
                              })
                            }
                            disabled={isPending}
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="colorCode">Kode Warna</Label>
                          <div className="flex items-center space-x-2">
                            <Input
                              id="colorCode"
                              type="color"
                              value={newVariant.colorCode || "#000000"}
                              onChange={(e) =>
                                setNewVariant({
                                  ...newVariant,
                                  colorCode: e.target.value,
                                })
                              }
                              className="w-10 h-9 p-1"
                              disabled={isPending}
                            />
                            <Input
                              value={newVariant.colorCode || "#000000"}
                              onChange={(e) =>
                                setNewVariant({
                                  ...newVariant,
                                  colorCode: e.target.value,
                                })
                              }
                              placeholder="#000000"
                              className="flex-1"
                              disabled={isPending}
                            />
                          </div>
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="variantSku">
                            SKU Varian (Opsional)
                          </Label>
                          <Input
                            id="variantSku"
                            placeholder="Contoh: RED001"
                            value={newVariant.sku || ""}
                            onChange={(e) =>
                              setNewVariant({
                                ...newVariant,
                                sku: e.target.value,
                              })
                            }
                            disabled={isPending}
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="variantStock">Stok</Label>
                          <Input
                            id="variantStock"
                            type="text"
                            inputMode="numeric"
                            pattern="[0-9]*"
                            placeholder="0"
                            value={newVariant.stock || 0}
                            onChange={(e) => {
                              // Only allow numeric input
                              const value = e.target.value.replace(
                                /[^0-9]/g,
                                ""
                              );
                              setNewVariant({
                                ...newVariant,
                                stock: value ? parseInt(value) : 0,
                              });
                            }}
                            disabled={isPending}
                          />
                        </div>
                        <div className="space-y-2 md:col-span-2">
                          <Label htmlFor="variantPrice">
                            Harga Khusus Varian (Opsional)
                          </Label>
                          <Input
                            id="variantPrice"
                            type="text"
                            inputMode="numeric"
                            pattern="[0-9]*"
                            placeholder="Kosongkan untuk menggunakan harga dasar produk"
                            value={newVariant.price || ""}
                            onChange={(e) => {
                              // Allow numeric input with decimal point
                              const value = e.target.value.replace(
                                /[^0-9.]/g,
                                ""
                              );
                              // Ensure only one decimal point
                              const sanitized =
                                value.split(".").length > 2
                                  ? value.replace(/\./g, (_, index) =>
                                      index === value.indexOf(".") ? "." : ""
                                    )
                                  : value;

                              setNewVariant({
                                ...newVariant,
                                price: sanitized
                                  ? parseFloat(sanitized)
                                  : undefined,
                              });
                            }}
                            disabled={isPending}
                          />
                          <p className="text-xs text-muted-foreground mt-1">
                            Jika dikosongkan, akan menggunakan harga dasar
                            produk
                          </p>
                        </div>
                      </div>
                      <div className="flex justify-end space-x-2">
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={handleCancelVariant}
                          disabled={isPending}
                          className="cursor-pointer"
                        >
                          <X className="h-4 w-4 mr-2" />
                          Batal
                        </Button>
                        <Button
                          type="button"
                          size="sm"
                          onClick={handleSaveVariant}
                          disabled={
                            isPending ||
                            !newVariant.colorName ||
                            !newVariant.colorCode
                          }
                          className="cursor-pointer"
                        >
                          <Check className="h-4 w-4 mr-2" />
                          {editingIndex !== null ? "Simpan" : "Tambahkan"}
                        </Button>
                      </div>
                    </div>
                  )}

                  {/* Variant Recommendation */}
                  <div className="text-xs text-muted-foreground bg-muted/50 p-3 rounded-md">
                    <span className="font-medium">Rekomendasi:</span> Varian
                    warna dapat digunakan untuk produk dengan berbagai pilihan
                    warna seperti baju, sepatu, atau aksesoris. Setiap varian
                    dapat memiliki stok dan harga yang berbeda.
                  </div>
                </div>
              )}
            </div>
          </AccordionContent>
        </AccordionItem>
      </Accordion>
    </div>
  );
};

export default ProductColorVariants;
