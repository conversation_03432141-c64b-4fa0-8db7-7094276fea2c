export interface Category {
  id: string;
  name: string;
}

export interface ProductVariant {
  id: string;
  sku: string | null;
  colorName: string;
  colorCode: string;
  price: number | null;
  stock: number;
  image: string | null;
  productId: string;
}

export interface Tag {
  id: string;
  name: string;
}

export interface Product {
  id: string;
  name: string;
  description: string | null;
  sku: string | null;
  price: number;
  discountPrice: number | null;
  cost: number | null;
  stock: number;
  createdAt: Date;
  updatedAt: Date;
  categoryId: string | null;
  category: Category | null;
  isDraft?: boolean;
  hasVariants?: boolean;
  variants?: ProductVariant[];
  tags?: Tag[];
  barcode?: string | null;
  image?: string | null;
  unit?: string;
  unitId?: string | null;
}

export interface StockCounts {
  available: number;
  low: number;
  outOfStock: number;
  needsApproval: number;
  drafts: number;
}

export interface ColumnVisibility {
  image: boolean;
  name: boolean;
  sku: boolean;
  barcode: boolean;
  category: boolean;
  tags: boolean;
  colorVariants: boolean;
  price: boolean;
  stock: boolean;
  stockStatus: boolean;
  cost: boolean;
  sellPrice: boolean;
  discountPrice: boolean;
  unit: boolean;
}
