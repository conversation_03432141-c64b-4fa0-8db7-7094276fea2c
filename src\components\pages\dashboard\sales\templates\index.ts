import { renderHorizontalTemplate1 } from "./HorizontalTemplate1";
import { renderVerticalTemplate1 } from "./VerticalTemplate1";
import type { Sale } from "../types";

/**
 * Template options for invoice printing
 */
export type TemplateType =
  | "horizontal1"
  | "horizontal2"
  | "horizontal3"
  | "horizontal4"
  | "vertical1"
  | "vertical2"
  | "vertical3"
  | "vertical4";

/**
 * Template orientation options
 */
export type TemplateOrientation = "horizontal" | "vertical";

/**
 * Get the appropriate template based on the selected template type
 */
export const getInvoiceTemplate = (
  templateType: TemplateType,
  sale: Sale
): string => {
  switch (templateType) {
    // Horizontal templates
    case "horizontal1":
      return renderHorizontalTemplate1(sale);
    case "horizontal2":
      return renderHorizontalTemplate1(sale);
    case "horizontal3":
      return renderHorizontalTemplate1(sale);

    // Vertical templates
    case "vertical1":
      return renderVerticalTemplate1(sale);
    case "vertical2":
      return renderVerticalTemplate1(sale);
    case "vertical3":
      return renderVerticalTemplate1(sale);

    // Default to horizontal1
    default:
      return renderHorizontalTemplate1(sale); // seharusnya 'horizontal1'
  }
};

/**
 * Get template display information for the UI
 */
export const getTemplateInfo = (templateType: TemplateType) => {
  switch (templateType) {
    // Horizontal templates
    case "horizontal1":
      return {
        name: "Template 1",
        description:
          "Tampilan tradisional dengan header tengah dan tabel berbordir",
        orientation: "horizontal" as TemplateOrientation,
      };
    case "horizontal2":
      return {
        name: "Template 2",
        description: "Tampilan modern dengan warna biru dan kotak informasi",
        orientation: "horizontal" as TemplateOrientation,
      };
    case "horizontal3":
      return {
        name: "Template 3",
        description:
          "Tampilan bersih dengan tipografi elegan dan garis minimal",
        orientation: "horizontal" as TemplateOrientation,
      };
    case "horizontal4":
      return {
        name: "Template 4",
        description:
          "Tampilan modern dengan kotak informasi dan desain yang elegan",
        orientation: "horizontal" as TemplateOrientation,
      };

    // Vertical templates
    case "vertical1":
      return {
        name: "Template 1",
        description:
          "Tampilan tradisional dengan header tengah dan tabel berbordir",
        orientation: "vertical" as TemplateOrientation,
      };
    case "vertical2":
      return {
        name: "Template 2",
        description: "Tampilan modern dengan warna biru dan kotak informasi",
        orientation: "vertical" as TemplateOrientation,
      };
    case "vertical3":
      return {
        name: "Template 3",
        description:
          "Tampilan bersih dengan tipografi elegan dan garis minimal",
        orientation: "vertical" as TemplateOrientation,
      };
    case "vertical4":
      return {
        name: "Template 4",
        description:
          "Tampilan modern dengan format struk kasir dan desain minimalis",
        orientation: "vertical" as TemplateOrientation,
      };

    // Default
    default:
      return {
        name: "Template 1",
        description:
          "Tampilan tradisional dengan header tengah dan tabel berbordir",
        orientation: "horizontal" as TemplateOrientation,
      };
  }
};
