import { Resend } from "resend";
import { generateNotificationEmail } from "./email-templates/notification-email";
import { db } from "./prisma";
import { NotificationType } from "@prisma/client";
import { getUserById } from "./user";

const resend = new Resend(process.env.RESEND_API_KEY);

interface SendNotificationEmailParams {
  userId: string;
  title: string;
  message: string;
  type: "info" | "warning" | "success" | "error";
}

export async function sendNotificationEmail({
  userId,
  title,
  message,
  type,
}: SendNotificationEmailParams): Promise<{
  success: boolean;
  error?: string;
}> {
  try {
    // Get user information
    const user = await getUserById(userId);
    
    if (!user || !user.email) {
      return { 
        success: false, 
        error: "User tidak ditemukan atau tidak memiliki email" 
      };
    }

    // Get user notification settings from database
    const notificationSettings = await db.notificationSettings.findUnique({
      where: { userId },
    });

    // If user has no notification settings or email notifications are disabled, don't send email
    if (!notificationSettings || !notificationSettings.emailEnabled) {
      return { 
        success: false, 
        error: "Notifikasi email tidak diaktifkan untuk pengguna ini" 
      };
    }

    // Check if this type of notification is enabled
    const shouldSendEmail = (() => {
      switch (type) {
        case "info":
          return notificationSettings.emailInfoEnabled;
        case "warning":
          return notificationSettings.emailWarningEnabled;
        case "success":
          return notificationSettings.emailSuccessEnabled;
        case "error":
          return notificationSettings.emailErrorEnabled;
        default:
          return false;
      }
    })();

    if (!shouldSendEmail) {
      return { 
        success: false, 
        error: `Notifikasi email tipe ${type} tidak diaktifkan untuk pengguna ini` 
      };
    }

    // Generate email HTML
    const emailHtml = generateNotificationEmail({
      title,
      message,
      type,
      recipientName: user.name || user.username || "Pengguna",
    });

    // Send email using Resend
    const { data, error } = await resend.emails.send({
      from: "Notifikasi <<EMAIL>>",
      to: user.email,
      subject: title,
      html: emailHtml,
    });

    if (error) {
      console.error("Error sending notification email:", error);
      return { success: false, error: "Gagal mengirim email notifikasi" };
    }

    return { success: true };
  } catch (error) {
    console.error("Error in sendNotificationEmail:", error);
    return { 
      success: false, 
      error: error instanceof Error ? error.message : "Unknown error" 
    };
  }
}
