import React from "react";
import DashboardLayout from "@/components/layout/dashboardlayout";
import SalesPage from "@/components/pages/dashboard/sales/sales";
import { getSales } from "@/actions/entities/sales";
import { Sale } from "@/components/pages/dashboard/sales/types";
import { Metadata } from "next";

export const metadata: Metadata = {
  title: "Penjualan - Kasir Online",
  description: "Kelola transaksi penjualan",
};

// This is an async Server Component
export default async function Sales() {
  // Fetch sales data
  const { sales, error } = await getSales();

  if (error) {
    console.error("Error fetching sales:", error);
    return <p>Error: {error}</p>;
  }

  return (
    <DashboardLayout>
      {/* Pass the fetched sales to the client component */}
      <SalesPage sales={(sales || []) as Sale[]} />
    </DashboardLayout>
  );
}
