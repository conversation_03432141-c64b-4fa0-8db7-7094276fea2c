"use client";

import React, { useState, useEffect, useTransition } from "react";
import { useRouter } from "next/navigation";
import { useForm, useFieldArray } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { toast } from "sonner";
import { addSale } from "@/actions/entities/sales";
import { Form } from "@/components/ui/form";
import { Button } from "@/components/ui/button";
import Link from "next/link";
import { SaleFormValues, Product, EnhancedSaleSchema } from "./types";
import CombinedSaleForm from "./components/CombinedSaleForm";
import { ArrowLeft, Check, Save } from "lucide-react";
import { useUnsavedChangesWarning } from "@/hooks/useUnsavedChangesWarning";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";

// Props use imported types
interface EnhancedSalePageProps {
  products: Product[];
}

const EnhancedSalePage: React.FC<EnhancedSalePageProps> = ({ products }) => {
  const router = useRouter();
  const [isPending, startTransition] = useTransition();
  const [totalAmount, setTotalAmount] = useState<number>(0);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [isFormValid, setIsFormValid] = useState(false);

  // Initialize the form with enhanced schema
  const form = useForm<SaleFormValues>({
    resolver: zodResolver(EnhancedSaleSchema),
    defaultValues: {
      items: [
        {
          productId: "",
          quantity: 1,
          priceAtSale: 0,
          unit: "Buah",
          tax: "",
        },
      ],
      totalAmount: 0,
      customerId: "default", // Default to general customer
      customerNIK: "",
      customerNPWP: "",
      paymentMethod: "cash",
      amountPaid: 0,
      notes: "",
      // New fields
      transactionDate: new Date(),
      transactionNumber: "",
      invoiceRef: "",
      tags: [],
      isDraft: false,
      memo: "",
      lampiran: [],
      priceIncludesTax: false,
    },
  });

  // Get the items field array
  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: "items",
  });

  // Watch for changes in the items array to calculate the total
  const items = form.watch("items");
  const formValues = form.watch();
  const totalAmountValue = form.watch("totalAmount");

  // Calculate total amount whenever items change
  useEffect(() => {
    const total = items.reduce(
      (sum: number, item: SaleFormValues["items"][number]) => {
        const quantity = item?.quantity ?? 0;
        const price = item?.priceAtSale ?? 0;
        return sum + quantity * price;
      },
      0
    );
    setTotalAmount(total);
    form.setValue("totalAmount", total);
  }, [items, form]);

  // Update totalAmount state when form value changes
  useEffect(() => {
    setTotalAmount(totalAmountValue);
  }, [totalAmountValue]);

  // Check form validity
  useEffect(() => {
    const isValid = form.formState.isValid;
    setIsFormValid(isValid);
  }, [form.formState.isValid]);

  // Check for unsaved changes
  useEffect(() => {
    // Consider form has unsaved changes if any meaningful data is entered
    const hasChanges = !!(
      formValues.items.some((item) => item.productId) ||
      formValues.customerId !== "default" ||
      formValues.customerEmail ||
      formValues.customerRefNumber ||
      formValues.shippingAddress ||
      (formValues.tags && formValues.tags.length > 0)
    );
    setHasUnsavedChanges(hasChanges);
  }, [formValues]);

  // Use the unsaved changes warning hook
  const {
    showExitDialog,
    setShowExitDialog,
    handleNavigation,
    confirmNavigation,
    cancelNavigation,
  } = useUnsavedChangesWarning(hasUnsavedChanges);

  // Handle product selection
  const handleProductChange = (index: number, productId: string) => {
    const selectedProduct: Product | undefined = products.find(
      (p) => p.id === productId
    );
    // Check if selectedProduct exists and its price is a number
    if (selectedProduct && typeof selectedProduct.price === "number") {
      const priceValue = selectedProduct.price; // Assign to variable
      form.setValue(`items.${index}.priceAtSale`, priceValue);

      // Force recalculation of total immediately
      const currentItems = form.getValues("items");

      // Ensure the item exists before trying to update it
      if (currentItems[index]) {
        currentItems[index].priceAtSale = priceValue;
      }

      const total = currentItems.reduce(
        (sum: number, item: SaleFormValues["items"][number]) => {
          const quantity = item?.quantity ?? 0;
          const price = item?.priceAtSale ?? 0;
          return sum + quantity * price;
        },
        0
      );

      setTotalAmount(total);
      form.setValue("totalAmount", total);
    }
  };

  // Handle form submission
  const onSubmit = async (values: SaleFormValues) => {
    // Manually validate all fields
    const isValid = await form.trigger();

    // If validation fails, show error messages
    if (!isValid) {
      const errors = form.formState.errors;

      // Check for product selection errors
      if (errors.items) {
        toast.error("Minimal satu produk harus dipilih");
      }

      // Check for specific item errors
      values.items.forEach((item, index) => {
        if (!item.productId) {
          toast.error(`Item #${index + 1}: Produk wajib dipilih`);
        }
        if (!item.quantity || item.quantity <= 0) {
          toast.error(`Item #${index + 1}: Jumlah harus lebih dari 0`);
        }
        if (!item.priceAtSale || item.priceAtSale <= 0) {
          toast.error(`Item #${index + 1}: Harga jual harus positif`);
        }
      });

      return; // Stop submission if there are errors
    }

    startTransition(async () => {
      try {
        // Extract the fields for the sale submission
        const saleData = {
          items: values.items,
          totalAmount: values.totalAmount,
          transactionNumber: values.transactionNumber,
          invoiceRef: values.invoiceRef,
          priceIncludesTax: values.priceIncludesTax,
          isDraft: values.isDraft,
        };

        const result = await addSale(saleData);
        if (result.success) {
          toast.success(result.success);
          form.reset(); // Reset form on success
          // Redirect after a short delay
          router.push("/dashboard/sales");
        } else if (result.error) {
          toast.error(result.error);
        } else {
          toast.error("Terjadi kesalahan yang tidak diketahui.");
        }
      } catch (error) {
        console.error("Submit Error:", error);
        toast.error("Gagal menghubungi server.");
      }
    });
  };

  // Handle saving as draft
  const handleSaveAsDraft = async () => {
    // Manually validate all fields
    const isValid = await form.trigger();

    // If validation fails, show error messages
    if (!isValid) {
      const errors = form.formState.errors;

      // Check for product selection errors
      if (errors.items) {
        toast.error("Minimal satu produk harus dipilih");
      }

      // Check for specific item errors
      const items = form.getValues("items");
      items.forEach((item, index) => {
        if (!item.productId) {
          toast.error(`Item #${index + 1}: Produk wajib dipilih`);
        }
        if (!item.quantity || item.quantity <= 0) {
          toast.error(`Item #${index + 1}: Jumlah harus lebih dari 0`);
        }
        if (!item.priceAtSale || item.priceAtSale <= 0) {
          toast.error(`Item #${index + 1}: Harga jual harus positif`);
        }
      });

      return; // Stop submission if there are errors
    }

    form.setValue("isDraft", true);
    form.handleSubmit(onSubmit)();
  };

  return (
    <div className="container mx-auto px-4 py-6">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h1 className="text-2xl font-bold">Tambah Penjualan Baru</h1>
          <p className="text-muted-foreground">
            Catat transaksi penjualan baru dengan mengisi detail di bawah ini
          </p>
        </div>
        <Button variant="outline" asChild className="gap-2">
          <Link href="/dashboard/sales">
            <ArrowLeft className="h-4 w-4" />
            Kembali
          </Link>
        </Button>
      </div>

      <Form {...form}>
        <form
          onSubmit={(e) => {
            e.preventDefault();

            // Trigger validation on all fields
            form.trigger().then((isValid) => {
              if (!isValid) {
                // Show toast messages for validation errors
                const errors = form.formState.errors;

                // Check for product selection errors
                if (errors.items) {
                  toast.error("Minimal satu produk harus dipilih");
                }

                // Check each item for validation
                items.forEach((item, index) => {
                  if (!item.productId) {
                    toast.error(`Item #${index + 1}: Produk wajib dipilih`);
                    // Set error on the field
                    form.setError(`items.${index}.productId`, {
                      type: "manual",
                      message: "Produk wajib dipilih",
                    });
                  }
                  if (!item.quantity || item.quantity <= 0) {
                    toast.error(
                      `Item #${index + 1}: Jumlah harus lebih dari 0`
                    );
                    // Set error on the field
                    form.setError(`items.${index}.quantity`, {
                      type: "manual",
                      message: "Jumlah harus lebih dari 0",
                    });
                  }
                  if (!item.priceAtSale || item.priceAtSale <= 0) {
                    toast.error(`Item #${index + 1}: Harga jual harus positif`);
                    // Set error on the field
                    form.setError(`items.${index}.priceAtSale`, {
                      type: "manual",
                      message: "Harga jual harus positif",
                    });
                  }
                });

                return;
              }

              // If no errors, proceed with form submission
              form.handleSubmit(onSubmit)(e);
            });
          }}
        >
          <div className="w-full">
            <CombinedSaleForm
              control={form.control}
              isPending={isPending}
              products={products}
              items={items}
              fields={fields}
              append={append}
              remove={remove}
              handleProductChange={handleProductChange}
              totalAmount={totalAmount}
            />
          </div>

          {/* Form Actions */}
          <div className="mt-8 pt-6 border-t border-gray-200 dark:border-gray-700 flex justify-end gap-4">
            <Button
              type="button"
              variant="outline"
              disabled={isPending}
              className="cursor-pointer"
              onClick={() => handleNavigation("/dashboard/sales")}
            >
              Batal
            </Button>
            <Button
              type="button"
              variant="outline"
              onClick={(e) => {
                // Trigger validation on all fields
                form.trigger().then((isValid) => {
                  if (!isValid) {
                    // Show toast messages for validation errors
                    const errors = form.formState.errors;

                    // Check for product selection errors
                    if (errors.items) {
                      toast.error("Minimal satu produk harus dipilih");
                    }

                    // Check each item for validation
                    items.forEach((item, index) => {
                      if (!item.productId) {
                        toast.error(`Item #${index + 1}: Produk wajib dipilih`);
                        // Set error on the field
                        form.setError(`items.${index}.productId`, {
                          type: "manual",
                          message: "Produk wajib dipilih",
                        });
                      }
                      if (!item.quantity || item.quantity <= 0) {
                        toast.error(
                          `Item #${index + 1}: Jumlah harus lebih dari 0`
                        );
                        // Set error on the field
                        form.setError(`items.${index}.quantity`, {
                          type: "manual",
                          message: "Jumlah harus lebih dari 0",
                        });
                      }
                      if (!item.priceAtSale || item.priceAtSale <= 0) {
                        toast.error(
                          `Item #${index + 1}: Harga jual harus positif`
                        );
                        // Set error on the field
                        form.setError(`items.${index}.priceAtSale`, {
                          type: "manual",
                          message: "Harga jual harus positif",
                        });
                      }
                    });

                    return;
                  }

                  // If no errors, proceed with saving as draft
                  form.setValue("isDraft", true);
                  form.handleSubmit(onSubmit)();
                });
              }}
              disabled={isPending}
              className="gap-2 cursor-pointer"
            >
              <Save className="h-4 w-4" />
              <span>Simpan Draft</span>
            </Button>
            <Button
              type="submit"
              disabled={isPending}
              className="gap-2 cursor-pointer"
            >
              {isPending ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  <span>Menyimpan...</span>
                </>
              ) : (
                <>
                  <Check className="h-4 w-4" />
                  <span>Simpan Penjualan</span>
                </>
              )}
            </Button>
          </div>
        </form>
      </Form>

      {/* Unsaved Changes Dialog */}
      <AlertDialog open={showExitDialog} onOpenChange={setShowExitDialog}>
        <AlertDialogContent className="w-[95%] max-w-md md:max-w-xl lg:max-w-2xl">
          <AlertDialogHeader>
            <AlertDialogTitle>Perubahan Belum Tersimpan</AlertDialogTitle>
            <AlertDialogDescription>
              Anda memiliki perubahan yang belum tersimpan. Jika Anda
              meninggalkan halaman ini, perubahan Anda akan hilang.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter className="flex flex-col space-y-2 md:flex-row md:space-y-0 md:space-x-2 md:justify-end">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-2 w-full">
              <AlertDialogCancel
                onClick={cancelNavigation}
                className="cursor-pointer w-full"
              >
                Kembali ke Form
              </AlertDialogCancel>
              <AlertDialogAction
                onClick={confirmNavigation}
                className="cursor-pointer bg-red-500 hover:bg-red-600 w-full"
              >
                Buang Perubahan
              </AlertDialogAction>
              <Button
                type="button"
                variant="default"
                className="cursor-pointer w-full"
                onClick={() => {
                  handleSaveAsDraft();
                  setShowExitDialog(false);
                }}
              >
                <Save className="h-4 w-4 mr-2" />
                Simpan ke Draft
              </Button>
            </div>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
};

export default EnhancedSalePage;
