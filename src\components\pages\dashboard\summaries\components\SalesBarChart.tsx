"use client";

import React from "react";
import { BarChartComponent, formatCurrency } from "./BarChartComponent";
import { Button } from "@/components/ui/button";
import { Card, CardFooter } from "@/components/ui/card";
import Link from "next/link";

export interface SalesChartDataPoint {
  name: string;
  total: number;
}

interface SalesBarChartProps {
  data: SalesChartDataPoint[];
  title?: string;
  description?: string;
  showFooter?: boolean;
  className?: string;
}

export function SalesBarChart({
  data,
  title = "Tren Penjualan",
  description = "Penjualan 6 bulan terakhir",
  showFooter = false,
  className = "",
}: SalesBarChartProps) {
  // Transform data to ensure it works with the BarChartComponent
  const chartData = data.map(item => ({
    name: item.name,
    total: item.total
  }));

  return (
    <Card className={`border-none shadow-md dark:bg-gray-800 ${className}`}>
      <BarChartComponent
        data={chartData}
        title={title}
        description={description}
        dataKeys={["total"]}
        valueFormatter={formatCurrency}
        showLegend={false}
      />
      
      {showFooter && (
        <CardFooter>
          <Button asChild variant="outline" size="sm">
            <Link href="/dashboard/sales">Lihat Laporan Lengkap</Link>
          </Button>
        </CardFooter>
      )}
    </Card>
  );
}
