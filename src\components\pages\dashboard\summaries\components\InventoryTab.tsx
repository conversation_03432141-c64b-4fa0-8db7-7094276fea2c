"use client";

import React from "react";
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import Link from "next/link";
import { ProductDistributionBarChart } from "./ProductDistributionBarChart";
import { DateRangeFilter, DateRange } from "./DateRangeFilter";
import { BarChartComponent } from "./BarChartComponent";

export interface ProductDistributionDataPoint {
  name: string;
  value: number;
}

interface InventoryTabProps {
  productDistData: ProductDistributionDataPoint[];
  className?: string;
}

export function InventoryTab({
  productDistData,
  className = "",
}: InventoryTabProps) {
  const [dateRange, setDateRange] = React.useState<DateRange>("30days");

  // Create additional data for the stock level analysis
  // This would normally come from an API, but we're creating mock data here
  const stockLevelData = [
    { name: "Elektronik", inStock: 120, lowStock: 15, outOfStock: 5 },
    { name: "Pakaian", inStock: 200, lowStock: 30, outOfStock: 10 },
    { name: "Makanan", inStock: 80, lowStock: 20, outOfStock: 8 },
    { name: "Minuman", inStock: 60, lowStock: 10, outOfStock: 2 },
    { name: "Lainnya", inStock: 40, lowStock: 5, outOfStock: 3 },
  ];

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Date Range Filter */}
      <div className="flex justify-end">
        <DateRangeFilter
          selectedRange={dateRange}
          onRangeChange={setDateRange}
        />
      </div>

      {/* Main Product Distribution Chart */}
      <Card className="border-none shadow-md dark:bg-gray-800">
        <CardHeader>
          <CardTitle>Analisis Inventaris</CardTitle>
          <CardDescription>
            Distribusi produk berdasarkan kategori
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="h-[400px]">
            <ProductDistributionBarChart
              data={productDistData}
              title="Distribusi Produk Detail"
              description="Analisis inventaris berdasarkan kategori"
            />
          </div>
        </CardContent>
        <CardFooter>
          <Button asChild variant="outline" size="sm">
            <Link href="/dashboard/products">Kelola Inventaris</Link>
          </Button>
        </CardFooter>
      </Card>

      {/* Stock Level Analysis Chart */}
      <Card className="border-none shadow-md dark:bg-gray-800">
        <CardHeader>
          <CardTitle>Analisis Level Stok</CardTitle>
          <CardDescription>
            Status stok berdasarkan kategori
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="h-[400px]">
            <BarChartComponent
              data={stockLevelData}
              dataKeys={["inStock", "lowStock", "outOfStock"]}
              showLegend={true}
              valueFormatter={(value) => value.toString()}
            />
          </div>
        </CardContent>
        <CardFooter>
          <Button asChild variant="outline" size="sm">
            <Link href="/dashboard/products">Kelola Stok</Link>
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
}
