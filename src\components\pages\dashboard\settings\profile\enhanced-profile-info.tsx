"use client";

import { useState, useEffect } from "react";
import { User } from "./types";
import { format } from "date-fns";
import { id } from "date-fns/locale";
import { useRouter } from "next/navigation";
import {
  Clock,
  CheckCircle2,
  Activity,
  ChevronRight,
  LogIn,
  Monitor,
  MapPin,
  Loader2,
  UserCog,
  KeyIcon,
  LogOut,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import {
  Card,
  CardContent,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import {
  getUserActivities,
  UserActivity as UserActivityType,
} from "@/actions/users/activity";

interface EnhancedProfileInfoProps {
  user: User;
}

export default function EnhancedProfileInfo({
  user,
}: EnhancedProfileInfoProps) {
  const router = useRouter();
  // Format date for display
  const formatDate = (date: Date | null | undefined) => {
    if (!date) return "-";
    return format(new Date(date), "d MMMM yyyy, HH:mm", { locale: id });
  };

  // Calculate profile completion percentage
  const calculateProfileCompletion = () => {
    const fields = [
      user.name,
      user.email,
      user.username,
      user.image,
      user.phone,
      user.bio,
      user.birthday,
    ];

    const completedFields = fields.filter(
      (field) => field !== null && field !== ""
    ).length;
    return Math.round((completedFields / fields.length) * 100);
  };

  const profileCompletionPercentage = calculateProfileCompletion();

  // State for user activities
  const [activities, setActivities] = useState<UserActivityType[]>([]);
  const [isLoadingActivities, setIsLoadingActivities] = useState(true);
  const [activityError, setActivityError] = useState<string | null>(null);

  // Fetch user activities
  useEffect(() => {
    const fetchActivities = async () => {
      try {
        setIsLoadingActivities(true);
        const result = await getUserActivities(5);

        if (result.success && result.data) {
          setActivities(result.data);
          setActivityError(null);
        } else {
          setActivityError(result.error || "Gagal mengambil aktivitas");
          setActivities([]);
        }
      } catch (error) {
        console.error("Error fetching activities:", error);
        setActivityError("Terjadi kesalahan saat mengambil aktivitas");
        setActivities([]);
      } finally {
        setIsLoadingActivities(false);
      }
    };

    fetchActivities();
  }, []);

  // Get icon based on activity type
  const getActivityIcon = (type: string) => {
    switch (type) {
      case "login":
        return <LogIn className="h-4 w-4 text-green-500" />;
      case "profile_update":
        return <UserCog className="h-4 w-4 text-blue-500" />;
      case "password_change":
        return <KeyIcon className="h-4 w-4 text-yellow-500" />;
      case "session_revoked":
        return <LogOut className="h-4 w-4 text-red-500" />;
      default:
        return <Activity className="h-4 w-4 text-gray-500" />;
    }
  };

  return (
    <div className="space-y-6">
      {/* Last Login Information */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-base font-medium flex items-center gap-2">
            <Clock className="h-4 w-4 text-indigo-500" />
            Informasi Login Terakhir
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="bg-indigo-50 dark:bg-indigo-900/20 p-2 rounded-full">
                  <Monitor className="h-5 w-5 text-indigo-500 dark:text-indigo-400" />
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-900 dark:text-gray-100">
                    {user.lastLogin ? "Login Terakhir" : "Belum ada data login"}
                  </p>
                  <p className="text-xs text-gray-500 dark:text-gray-400">
                    {user.lastLogin
                      ? formatDate(user.lastLogin)
                      : "Login pertama Anda akan tercatat di sini"}
                  </p>
                </div>
              </div>
              <Badge
                variant="outline"
                className="bg-green-50 text-green-700 border-green-200 dark:bg-green-900/20 dark:text-green-300 dark:border-green-800/30"
              >
                Aktif
              </Badge>
            </div>

            {user.lastLogin && (
              <div className="flex items-center gap-3 mt-3">
                <div className="bg-gray-50 dark:bg-gray-800 p-2 rounded-full">
                  <MapPin className="h-4 w-4 text-gray-500" />
                </div>
                <div>
                  <p className="text-xs text-gray-500 dark:text-gray-400">
                    Jakarta, Indonesia • Chrome di Windows
                  </p>
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Profile Completion Progress */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-base font-medium flex items-center gap-2">
            <CheckCircle2 className="h-4 w-4 text-indigo-500" />
            Kelengkapan Profil
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div>
              <div className="flex justify-between mb-1">
                <p className="text-sm font-medium text-gray-900 dark:text-gray-100">
                  Profil Anda {profileCompletionPercentage}% lengkap
                </p>
                <span className="text-sm font-medium text-indigo-600 dark:text-indigo-400">
                  {profileCompletionPercentage}%
                </span>
              </div>
              <Progress value={profileCompletionPercentage} className="h-2" />
            </div>

            <div className="space-y-2 mt-3">
              {!user.phone && (
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <CheckCircle2 className="h-4 w-4 text-gray-300 dark:text-gray-600" />
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      Tambahkan nomor telepon
                    </p>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-7 text-xs cursor-pointer"
                  >
                    Tambah
                  </Button>
                </div>
              )}

              {!user.bio && (
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <CheckCircle2 className="h-4 w-4 text-gray-300 dark:text-gray-600" />
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      Tambahkan bio
                    </p>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-7 text-xs cursor-pointer"
                  >
                    Tambah
                  </Button>
                </div>
              )}

              {!user.birthday && (
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <CheckCircle2 className="h-4 w-4 text-gray-300 dark:text-gray-600" />
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      Tambahkan tanggal lahir
                    </p>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-7 text-xs cursor-pointer"
                  >
                    Tambah
                  </Button>
                </div>
              )}

              {!user.image && (
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <CheckCircle2 className="h-4 w-4 text-gray-300 dark:text-gray-600" />
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      Unggah foto profil
                    </p>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-7 text-xs cursor-pointer"
                  >
                    Unggah
                  </Button>
                </div>
              )}

              {profileCompletionPercentage === 100 && (
                <div className="flex items-center gap-2 text-green-600 dark:text-green-400">
                  <CheckCircle2 className="h-4 w-4" />
                  <p className="text-sm font-medium">
                    Profil Anda sudah lengkap!
                  </p>
                </div>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Activity Summary */}
      <Card>
        <CardHeader className="pb-3">
          <div className="flex justify-between items-center">
            <CardTitle className="text-base font-medium flex items-center gap-2">
              <Activity className="h-4 w-4 text-indigo-500" />
              Aktivitas Terbaru
            </CardTitle>
            <Button
              variant="ghost"
              size="sm"
              className="text-xs cursor-pointer"
              onClick={() => router.push("/dashboard/activity")}
            >
              Lihat Semua
              <ChevronRight className="h-4 w-4 ml-1" />
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {isLoadingActivities ? (
              <div className="flex flex-col items-center justify-center py-8">
                <Loader2 className="h-8 w-8 animate-spin text-primary mb-4" />
                <p className="text-sm text-muted-foreground">
                  Memuat aktivitas...
                </p>
              </div>
            ) : activityError ? (
              <div className="text-center py-6">
                <p className="text-sm text-red-500 dark:text-red-400">
                  {activityError}
                </p>
              </div>
            ) : activities.length === 0 ? (
              <div className="text-center py-6">
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  Belum ada aktivitas
                </p>
              </div>
            ) : (
              <div className="space-y-4">
                {activities.map((activity, index) => (
                  <div key={activity.id}>
                    <div className="flex items-start gap-3">
                      <div className="bg-gray-100 dark:bg-gray-800 p-2 rounded-full mt-0.5">
                        {getActivityIcon(activity.type)}
                      </div>
                      <div className="flex-1">
                        <div className="flex justify-between items-start">
                          <div>
                            <p className="text-sm font-medium text-gray-900 dark:text-gray-100">
                              {activity.description}
                            </p>
                            {activity.device && (
                              <p className="text-xs text-gray-500 dark:text-gray-400 mt-0.5">
                                {activity.device}
                              </p>
                            )}
                          </div>
                          <p className="text-xs text-gray-500 dark:text-gray-400">
                            {activity.timestamp}
                          </p>
                        </div>
                      </div>
                    </div>
                    {index < activities.length - 1 && (
                      <Separator className="my-4" />
                    )}
                  </div>
                ))}
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
