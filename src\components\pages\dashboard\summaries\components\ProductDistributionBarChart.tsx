"use client";

import React from "react";
import { BarChartComponent, CHART_COLORS } from "./BarChartComponent";
import { Button } from "@/components/ui/button";
import { Card, CardFooter } from "@/components/ui/card";
import Link from "next/link";

export interface ProductDistributionDataPoint {
  name: string;
  value: number;
}

interface ProductDistributionBarChartProps {
  data: ProductDistributionDataPoint[];
  title?: string;
  description?: string;
  showFooter?: boolean;
  className?: string;
}

export function ProductDistributionBarChart({
  data,
  title = "Distribusi Produk",
  description = "Berdasarkan kategori",
  showFooter = false,
  className = "",
}: ProductDistributionBarChartProps) {
  // Transform data to work with BarChartComponent
  const chartData = data.map(item => ({
    name: item.name,
    jumlah: item.value
  }));

  // Custom formatter for product counts (no currency)
  const formatCount = (value: number) => value.toString();

  return (
    <Card className={`border-none shadow-md dark:bg-gray-800 ${className}`}>
      <BarChartComponent
        data={chartData}
        title={title}
        description={description}
        dataKeys={["jumlah"]}
        valueFormatter={formatCount}
        colors={[CHART_COLORS[0]]} // Use the first color from the palette
        showLegend={false}
      />
      
      {showFooter && (
        <CardFooter>
          <Button asChild variant="outline" size="sm">
            <Link href="/dashboard/products">Kelola Inventaris</Link>
          </Button>
        </CardFooter>
      )}
    </Card>
  );
}
