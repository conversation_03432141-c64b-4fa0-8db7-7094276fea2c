import { auth } from "@/lib/auth";
import { redirect } from "next/navigation";
import React from "react";
import DashboardLayout from "@/components/layout/dashboardlayout";
import SummariesPage from "@/components/pages/dashboard/summaries/summaries";
import { Metadata } from "next";

export const metadata: Metadata = {
  title: "Ringkasan | Kasir Online",
  description: "Ringkasan bisnis dan analitik Anda",
};

const Summaries = async () => {
  const session = await auth();
  if (!session) redirect("/login");

  return (
    <DashboardLayout>
      <SummariesPage />
    </DashboardLayout>
  );
};

export default Summaries;
