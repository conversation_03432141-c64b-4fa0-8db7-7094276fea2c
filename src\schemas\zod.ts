import { z } from "zod";

export const LoginSchema = z.object({
  email: z.string().email({ message: "Email tidak valid" }),
  password: z
    .string()
    .min(1, { message: "Password wajib diisi" })
    .min(6, "Password minimal 6 karakter"),
});

export const ProductSchema = z.object({
  name: z.string().min(1, { message: "Nama produk wajib diisi" }),
  description: z.string().optional(),
  sku: z.string().optional(),
  // Use coerce for string inputs from forms that should be numbers
  price: z.coerce
    .number({ invalid_type_error: "Harga jual harus berupa angka" })
    .nonnegative({ message: "Harga jual tidak boleh negatif" })
    .default(0),
  discountPrice: z.coerce
    .number({ invalid_type_error: "Harga diskon harus berupa angka" })
    .nonnegative({ message: "Harga diskon tidak boleh negatif" })
    .optional(),
  cost: z.coerce
    .number({ invalid_type_error: "Harga beli harus berupa angka" })
    .nonnegative({ message: "Harga beli tidak boleh negatif" })
    .default(0),
  stock: z.coerce
    .number({ invalid_type_error: "Stok harus berupa angka" })
    .int({ message: "Stok harus berupa bilangan bulat" })
    .nonnegative({ message: "Stok tidak boleh negatif" })
    .default(0),
  image: z.string().optional().default(""),
  weight: z.coerce
    .number({ invalid_type_error: "Berat harus berupa angka" })
    .nonnegative({ message: "Berat tidak boleh negatif" })
    .optional(),
  length: z.coerce
    .number({ invalid_type_error: "Panjang harus berupa angka" })
    .nonnegative({ message: "Panjang tidak boleh negatif" })
    .optional(),
  width: z.coerce
    .number({ invalid_type_error: "Lebar harus berupa angka" })
    .nonnegative({ message: "Lebar tidak boleh negatif" })
    .optional(),
  height: z.coerce
    .number({ invalid_type_error: "Tinggi harus berupa angka" })
    .nonnegative({ message: "Tinggi tidak boleh negatif" })
    .optional(),
  unit: z.string().default("Pcs"),
  tags: z.array(z.string()).optional().default([]),
  isDraft: z.boolean().default(false),
});

// Schema for sale item (individual product in a sale)
export const SaleItemSchema = z.object({
  productId: z.string().min(1, { message: "Produk wajib dipilih" }),
  quantity: z.coerce
    .number({ invalid_type_error: "Jumlah harus berupa angka" })
    .int({ message: "Jumlah harus berupa bilangan bulat" })
    .positive({ message: "Jumlah harus lebih dari 0" }),
  priceAtSale: z.coerce
    .number({ invalid_type_error: "Harga jual harus berupa angka" })
    .positive({ message: "Harga jual harus positif" }),
});

// Schema for the entire sale
export const SaleSchema = z.object({
  items: z
    .array(SaleItemSchema)
    .min(1, { message: "Minimal satu produk harus dipilih" }),
  totalAmount: z.coerce
    .number({ invalid_type_error: "Total harus berupa angka" })
    .positive({ message: "Total harus positif" }),
  transactionNumber: z.string().optional(),
  invoiceRef: z.string().optional(),
});

// Schema for purchase item (individual product in a purchase)
export const PurchaseItemSchema = z.object({
  productId: z.string().min(1, { message: "Produk wajib dipilih" }),
  quantity: z.coerce
    .number({ invalid_type_error: "Jumlah harus berupa angka" })
    .int({ message: "Jumlah harus berupa bilangan bulat" })
    .positive({ message: "Jumlah harus lebih dari 0" }),
  costAtPurchase: z.coerce
    .number({ invalid_type_error: "Harga beli harus berupa angka" })
    .positive({ message: "Harga beli harus positif" }),
  unit: z.string().optional().default("Buah"),
  tax: z.string().optional(),
});

// Schema for the entire purchase
export const PurchaseSchema = z.object({
  items: z
    .array(PurchaseItemSchema)
    .min(1, { message: "Minimal satu produk harus dipilih" }),
  totalAmount: z.coerce
    .number({ invalid_type_error: "Total harus berupa angka" })
    .positive({ message: "Total harus positif" }),
  invoiceRef: z.string().optional(),
  supplierId: z.string().min(1, { message: "Supplier wajib dipilih" }),
  isDraft: z.boolean().default(false),
  // New fields
  supplierEmail: z
    .string()
    .optional()
    .refine((val) => !val || /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(val), {
      message: "Email tidak valid",
    }),
  transactionDate: z.date().default(() => new Date()),
  paymentDueDate: z.date().optional(),
  transactionNumber: z.string().optional(),
  tags: z.array(z.string()).optional().default([]),
  billingAddress: z.string().optional(),
  warehouse: z.string().optional(), // This is used in the UI but not stored in the database

  memo: z.string().optional(),
  lampiran: z
    .array(
      z.object({
        url: z.string(),
        filename: z.string(),
      })
    )
    .optional()
    .default([]),
});

export const CustomerSchema = z.object({
  // Basic Info
  name: z.string().min(1, { message: "Nama pelanggan wajib diisi" }),
  firstName: z.string().optional(),
  middleName: z.string().optional(),
  lastName: z.string().optional(),
  contactName: z.string().optional(),

  // Contact Information
  phone: z.string().optional(),
  telephone: z.string().optional(),
  fax: z.string().optional(),
  email: z
    .string()
    .email({ message: "Email tidak valid" })
    .optional()
    .or(z.literal("")),

  // Identity Information
  identityType: z.string().optional(),
  identityNumber: z.string().optional(),
  NIK: z.string().optional(),
  NPWP: z.string().optional(),

  // Company Information
  companyName: z.string().optional(),
  otherInfo: z.string().optional(),

  // Address Information
  address: z.string().optional(), // Keep for backward compatibility
  billingAddress: z.string().optional(),
  shippingAddress: z.string().optional(),
  sameAsShipping: z.boolean().default(false),

  // Bank Information
  bankName: z.string().optional(),
  bankBranch: z.string().optional(),
  accountHolder: z.string().optional(),
  accountNumber: z.string().optional(),

  // Additional fields
  notes: z.string().optional(),
});

export const SupplierSchema = z.object({
  // Basic Info
  name: z.string().min(1, { message: "Nama supplier wajib diisi" }),
  firstName: z.string().optional(),
  middleName: z.string().optional(),
  lastName: z.string().optional(),
  contactName: z.string().optional(),

  // Contact Information
  phone: z.string().optional(),
  telephone: z.string().optional(),
  fax: z.string().optional(),
  email: z
    .string()
    .email({ message: "Email tidak valid" })
    .optional()
    .or(z.literal("")),

  // Identity Information
  identityType: z.string().optional(),
  identityNumber: z.string().optional(),
  NIK: z.string().optional(),
  NPWP: z.string().optional(),

  // Company Information
  companyName: z.string().optional(),
  otherInfo: z.string().optional(),

  // Address Information
  address: z.string().optional(), // Keep for backward compatibility
  billingAddress: z.string().optional(),
  shippingAddress: z.string().optional(),
  sameAsShipping: z.boolean().default(false),

  // Bank Information
  bankName: z.string().optional(),
  bankBranch: z.string().optional(),
  accountHolder: z.string().optional(),
  accountNumber: z.string().optional(),

  // Additional fields
  notes: z.string().optional(),
});

export const DaftarSchema = z.object({
  username: z.string().min(1, { message: "Username wajib diisi" }),
  email: z.string().email({ message: "Email tidak valid" }),
  password: z
    .string()
    .min(1, { message: "Password wajib diisi" })
    .min(6, "Password minimal 6 karakter"),
});

export const EmployeeLoginSchema = z.object({
  companyUsername: z
    .string()
    .min(1, { message: "Username perusahaan wajib diisi" }),
  employeeId: z.string().min(1, { message: "ID karyawan wajib diisi" }),
  password: z
    .string()
    .min(1, { message: "Password wajib diisi" })
    .min(6, "Password minimal 6 karakter"),
});

export const CreateEmployeeSchema = z.object({
  name: z.string().min(1, { message: "Nama karyawan wajib diisi" }),
  employeeId: z.string().min(1, { message: "ID karyawan wajib diisi" }),
  password: z
    .string()
    .min(1, { message: "Password wajib diisi" })
    .min(6, "Password minimal 6 karakter"),
  role: z.enum(["ADMIN", "CASHIER"], {
    required_error: "Role wajib dipilih",
    invalid_type_error: "Role tidak valid",
  }),
});

// Schema for updating employee information
export const UpdateEmployeeNameSchema = z.object({
  name: z.string().min(1, { message: "Nama karyawan wajib diisi" }),
  role: z.enum(["ADMIN", "CASHIER"], {
    required_error: "Role wajib dipilih",
    invalid_type_error: "Role tidak valid",
  }),
});

// Schema for updating employee password
export const UpdateEmployeePasswordSchema = z
  .object({
    password: z
      .string()
      .min(1, { message: "Password wajib diisi" })
      .min(6, "Password minimal 6 karakter"),
    confirmPassword: z
      .string()
      .min(1, { message: "Konfirmasi password wajib diisi" }),
  })
  .refine((data) => data.password === data.confirmPassword, {
    message: "Password dan konfirmasi password tidak sama",
    path: ["confirmPassword"],
  });
