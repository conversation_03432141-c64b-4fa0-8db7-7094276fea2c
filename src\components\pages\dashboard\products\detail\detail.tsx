"use client";

import type { NextPage } from "next";
import React from "react";
import DashboardLayout from "@/components/layout/dashboardlayout";

// Import our component modules
import ProductHeader from "../components/ProductHeader";
import ProductSummary from "../components/ProductSummary";
import ProductTabs from "../components/ProductTabs";
import ProductTimestamps from "../components/ProductTimestamps";

interface Category {
  id: string;
  name: string;
}

interface Product {
  id: string;
  name: string;
  description: string | null;
  sku: string | null;
  price: number;
  discountPrice: number | null;
  cost: number | null;
  stock: number;
  image: string | null;
  createdAt: Date;
  updatedAt: Date;
  categoryId: string | null;
  category: Category | null;
  barcode?: string;
  taxRate?: number;
  hasVariants?: boolean;
  trackInventory?: boolean;
  minStockLevel?: number;
  weight?: number | null;
  dimensions?: {
    length?: number;
    width?: number;
    height?: number;
  };
  tags?: string[];
  isDraft?: boolean;
}

interface ProductDetailPageProps {
  product: Product;
}

const ProductDetailPage: NextPage<ProductDetailPageProps> = ({ product }) => {
  return (
    <DashboardLayout>
      <div className="w-full px-4 py-6">
        {/* Header with back button and actions */}
        <ProductHeader
          productId={product.id}
          productName={product.name}
          isDraft={product.isDraft}
        />

        {/* Main content */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Left column - Image and summary */}
          <div className="lg:col-span-1 space-y-6">
            <ProductSummary
              name={product.name}
              image={product.image}
              price={product.price}
              discountPrice={product.discountPrice}
              cost={product.cost}
              stock={product.stock}
              minStockLevel={product.minStockLevel}
              category={product.category}
            />
          </div>

          {/* Right column - Tabs with details */}
          <div className="lg:col-span-2">
            <ProductTabs
              id={product.id}
              name={product.name}
              sku={product.sku}
              description={product.description}
              barcode={product.barcode}
              taxRate={product.taxRate}
              tags={product.tags}
              stock={product.stock}
              minStockLevel={product.minStockLevel}
              trackInventory={product.trackInventory}
              hasVariants={product.hasVariants}
              weight={product.weight}
              dimensions={product.dimensions}
            />
          </div>
        </div>

        {/* Timestamps */}
        <ProductTimestamps
          createdAt={product.createdAt}
          updatedAt={product.updatedAt}
        />
      </div>
    </DashboardLayout>
  );
};

export default ProductDetailPage;
