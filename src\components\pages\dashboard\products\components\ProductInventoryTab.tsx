"use client";

import React from "react";
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Layers, AlertCircle, Check, X, Package, Tag } from "lucide-react";
import { Separator } from "@/components/ui/separator";

interface ProductInventoryTabProps {
  stock: number;
  minStockLevel?: number;
  trackInventory?: boolean;
  hasVariants?: boolean;
}

const ProductInventoryTab: React.FC<ProductInventoryTabProps> = ({
  stock,
  minStockLevel,
  trackInventory,
  hasVariants,
}) => {
  // Determine stock status for visual indicators
  const getStockStatus = () => {
    if (stock <= 0) return "empty";
    if (minStockLevel && stock <= minStockLevel) return "low";
    return "normal";
  };

  const stockStatus = getStockStatus();

  return (
    <div className="space-y-6">
      {/* Stock Management Card - Modern Design */}
      <Card className="overflow-hidden border-none shadow-md">
        <CardHeader className="pt-4 pb-2 bg-gradient-to-r from-purple-50 to-indigo-50 dark:from-purple-950/30 dark:to-indigo-950/30 border-b">
          <CardTitle className="text-lg flex items-center gap-2">
            <Layers className="h-5 w-5 text-purple-500" />
            Manajemen Stok
          </CardTitle>
        </CardHeader>
        <CardContent className="p-6">
          <div className="flex flex-col space-y-6">
            {/* Stock Level with Visual Indicator */}
            <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-100 dark:border-gray-700 shadow-sm">
              <div className="flex items-center justify-between mb-2">
                <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">
                  Stok Saat Ini
                </h3>
                <Badge
                  variant={
                    stockStatus === "empty"
                      ? "destructive"
                      : stockStatus === "low"
                        ? "outline"
                        : "secondary"
                  }
                  className={
                    stockStatus === "low"
                      ? "bg-amber-100 text-amber-700 dark:bg-amber-900/20 dark:text-amber-400 border-amber-200 dark:border-amber-800"
                      : ""
                  }
                >
                  {stockStatus === "empty"
                    ? "Habis"
                    : stockStatus === "low"
                      ? "Stok Rendah"
                      : "Tersedia"}
                </Badge>
              </div>
              <div className="flex items-center">
                <div
                  className={`w-12 h-12 rounded-full flex items-center justify-center mr-4 ${
                    stockStatus === "empty"
                      ? "bg-red-100 dark:bg-red-900/30"
                      : stockStatus === "low"
                        ? "bg-amber-100 dark:bg-amber-900/30"
                        : "bg-green-100 dark:bg-green-900/30"
                  }`}
                >
                  {stockStatus === "empty" ? (
                    <AlertCircle className="h-6 w-6 text-red-600 dark:text-red-400" />
                  ) : stockStatus === "low" ? (
                    <AlertCircle className="h-6 w-6 text-amber-600 dark:text-amber-400" />
                  ) : (
                    <Check className="h-6 w-6 text-green-600 dark:text-green-400" />
                  )}
                </div>
                <div>
                  <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                    {stock}
                    <span className="text-sm font-normal text-gray-500 dark:text-gray-400">
                      {" "}
                      unit
                    </span>
                  </p>
                </div>
              </div>
            </div>

            {/* Inventory Settings */}
            <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-100 dark:border-gray-700 shadow-sm">
              <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-3">
                Pengaturan Inventaris
              </h3>
              <div className="space-y-3">
                {/* Minimum Stock Level */}
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <div className="w-8 h-8 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center mr-3">
                      <Package className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                    </div>
                    <span className="text-sm">Stok Minimum</span>
                  </div>
                  <div className="flex items-center">
                    <span className="font-medium">
                      {minStockLevel !== undefined ? minStockLevel : 0}
                    </span>
                    <span className="text-xs text-gray-500 dark:text-gray-400 ml-1">
                      unit
                    </span>
                  </div>
                </div>

                <Separator />

                {/* Track Inventory */}
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <div className="w-8 h-8 bg-indigo-100 dark:bg-indigo-900/30 rounded-full flex items-center justify-center mr-3">
                      <Tag className="h-4 w-4 text-indigo-600 dark:text-indigo-400" />
                    </div>
                    <span className="text-sm">Lacak Inventaris</span>
                  </div>
                  <div className="flex items-center">
                    {trackInventory ? (
                      <Badge className="bg-green-100 text-green-700 dark:bg-green-900/20 dark:text-green-400 border-green-200 dark:border-green-800">
                        <Check className="h-3 w-3 mr-1" /> Aktif
                      </Badge>
                    ) : (
                      <Badge variant="outline" className="text-gray-500">
                        <X className="h-3 w-3 mr-1" /> Nonaktif
                      </Badge>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Variants Card - Modern Design */}
      <Card className="overflow-hidden border-none shadow-md">
        <CardHeader className="pt-4 pb-2 bg-gradient-to-r from-blue-50 to-cyan-50 dark:from-blue-950/30 dark:to-cyan-950/30 border-b">
          <CardTitle className="text-lg flex items-center gap-2">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
              className="h-5 w-5 text-blue-500"
            >
              <rect x="3" y="3" width="6" height="6" rx="1" />
              <rect x="15" y="3" width="6" height="6" rx="1" />
              <rect x="3" y="15" width="6" height="6" rx="1" />
              <rect x="15" y="15" width="6" height="6" rx="1" />
            </svg>
            Varian Produk
          </CardTitle>
        </CardHeader>
        <CardContent className="p-6">
          <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-100 dark:border-gray-700 shadow-sm">
            <div className="flex items-center">
              <div
                className={`w-12 h-12 rounded-full flex items-center justify-center mr-4 ${hasVariants ? "bg-blue-100 dark:bg-blue-900/30" : "bg-gray-100 dark:bg-gray-800"}`}
              >
                {hasVariants ? (
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="h-6 w-6 text-blue-600 dark:text-blue-400"
                  >
                    <rect x="3" y="3" width="6" height="6" rx="1" />
                    <rect x="15" y="3" width="6" height="6" rx="1" />
                    <rect x="3" y="15" width="6" height="6" rx="1" />
                    <rect x="15" y="15" width="6" height="6" rx="1" />
                  </svg>
                ) : (
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="h-6 w-6 text-gray-400"
                  >
                    <rect x="8" y="8" width="8" height="8" rx="1" />
                  </svg>
                )}
              </div>
              <div>
                <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Status Varian
                </h3>
                {hasVariants ? (
                  <div className="flex items-center">
                    <Badge className="bg-blue-100 text-blue-700 dark:bg-blue-900/20 dark:text-blue-400 border-blue-200 dark:border-blue-800">
                      <Check className="h-3 w-3 mr-1" /> Memiliki Varian
                    </Badge>
                  </div>
                ) : (
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    Produk ini tidak memiliki varian
                  </p>
                )}
              </div>
            </div>

            {/* Variant Recommendation */}
            <div className="mt-4 bg-blue-50 dark:bg-blue-900/20 p-3 rounded-md">
              <p className="text-xs text-blue-700 dark:text-blue-300">
                <span className="font-medium">Rekomendasi:</span> Varian produk
                dapat digunakan untuk produk dengan berbagai pilihan seperti
                ukuran, warna, atau model. Contoh: Baju dengan varian ukuran S,
                M, L, XL atau Handphone dengan varian warna Hitam, Putih, Biru.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default ProductInventoryTab;
