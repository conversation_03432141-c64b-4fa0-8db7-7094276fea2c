"use client";

import React from "react";
import { <PERSON>, Card<PERSON>ontent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import Link from "next/link";
import { SalesBarChart } from "./SalesBarChart";
import { DateRangeFilter, DateRange } from "./DateRangeFilter";
import { BarChartComponent } from "./BarChartComponent";

export interface SalesChartDataPoint {
  name: string;
  total: number;
}

interface SalesTabProps {
  salesChartData: SalesChartDataPoint[];
  className?: string;
}

export function SalesTab({
  salesChartData,
  className = "",
}: SalesTabProps) {
  const [dateRange, setDateRange] = React.useState<DateRange>("30days");

  // Create additional data for the detailed sales analysis
  // This would normally come from an API, but we're creating mock data here
  const detailedSalesData = [
    { name: "<PERSON>", online: 4000, offline: 2400 },
    { name: "Feb", online: 3000, offline: 1398 },
    { name: "<PERSON>", online: 2000, offline: 9800 },
    { name: "Apr", online: 2780, offline: 3908 },
    { name: "May", online: 1890, offline: 4800 },
    { name: "Jun", online: 2390, offline: 3800 },
  ];

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Date Range Filter */}
      <div className="flex justify-end">
        <DateRangeFilter
          selectedRange={dateRange}
          onRangeChange={setDateRange}
        />
      </div>

      {/* Main Sales Chart */}
      <Card className="border-none shadow-md dark:bg-gray-800">
        <CardHeader>
          <CardTitle>Analisis Penjualan</CardTitle>
          <CardDescription>
            Performa penjualan berdasarkan periode
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="h-[400px]">
            <SalesBarChart
              data={salesChartData}
              title="Tren Penjualan Detail"
              description="Analisis penjualan 6 bulan terakhir"
            />
          </div>
        </CardContent>
        <CardFooter>
          <Button asChild variant="outline" size="sm">
            <Link href="/dashboard/sales">Lihat Laporan Lengkap</Link>
          </Button>
        </CardFooter>
      </Card>

      {/* Sales Comparison Chart */}
      <Card className="border-none shadow-md dark:bg-gray-800">
        <CardHeader>
          <CardTitle>Perbandingan Penjualan</CardTitle>
          <CardDescription>
            Penjualan online vs offline
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="h-[400px]">
            <BarChartComponent
              data={detailedSalesData}
              dataKeys={["online", "offline"]}
              showLegend={true}
            />
          </div>
        </CardContent>
        <CardFooter>
          <Button asChild variant="outline" size="sm">
            <Link href="/dashboard/sales">Lihat Detail Perbandingan</Link>
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
}
