import React from "react";
import { notFound } from "next/navigation";
import EnhancedPurchaseEditPage from "@/components/pages/dashboard/purchases/edit";
import PurchaseEditErrorFallback from "@/components/pages/dashboard/purchases/edit/error-fallback";
import { getPurchaseById } from "@/actions/entities/purchases";
import { getProducts } from "@/lib/get-products";
import { getSuppliers } from "@/lib/get-suppliers";
import DashboardLayout from "@/components/layout/dashboardlayout";
import { Metadata } from "next";

// Define metadata for the page
export const metadata: Metadata = {
  title: "Edit Pembelian - Kasir Online",
};

type Props = {
  params: Promise<{ id: string }>;
};

// This is an async Server Component
export default async function EditPurchase(props: Props) {
  // Get the id from params (which is now a Promise)
  const params = await props.params;
  const id = params.id;

  // Fetch the purchase with the given ID
  const purchaseResult = await getPurchaseById(id);

  // If purchase not found, return 404
  if (!purchaseResult.purchase) {
    notFound();
  }

  // Fetch products for the form
  const products = await getProducts({
    includeOutOfStock: true, // Show all products for purchases
    orderBy: "createdAt", // Order by creation date to get newest products
    orderDirection: "desc", // Newest first
    excludeDrafts: true, // Exclude draft products
  });

  // Fetch suppliers for the form
  const suppliersResult = await getSuppliers();

  // Check if products were loaded successfully
  if (!products) {
    return (
      <DashboardLayout>
        <PurchaseEditErrorFallback
          purchaseId={id}
          errorMessage="Gagal memuat data produk. Silakan coba lagi nanti."
        />
      </DashboardLayout>
    );
  }

  // Check if suppliers were loaded successfully
  if (suppliersResult.error) {
    return (
      <DashboardLayout>
        <PurchaseEditErrorFallback
          purchaseId={id}
          errorMessage={
            suppliersResult.error ||
            "Gagal memuat data supplier. Silakan coba lagi nanti."
          }
        />
      </DashboardLayout>
    );
  }

  // Create a properly typed purchase object that matches the Purchase interface
  // and ensure all Decimal objects are converted to numbers
  const serializedPurchase = {
    id: purchaseResult.purchase.id,
    totalAmount: purchaseResult.purchase.totalAmount,
    invoiceRef: purchaseResult.purchase.invoiceRef,
    purchaseDate: purchaseResult.purchase.purchaseDate,
    supplierId: purchaseResult.purchase.supplierId,
    isDraft: purchaseResult.purchase.isDraft,
    // Optional fields with defaults
    supplierEmail: purchaseResult.purchase.supplierEmail || "",
    transactionDate:
      purchaseResult.purchase.transactionDate || new Date().toISOString(),
    paymentDueDate: purchaseResult.purchase.paymentDueDate || null,
    transactionNumber: purchaseResult.purchase.transactionNumber || "",
    tags: purchaseResult.purchase.tags || [],
    billingAddress: purchaseResult.purchase.billingAddress || "",
    warehouse: "", // Warehouse is not stored in the database
    // Additional fields
    memo: purchaseResult.purchase.memo || "",
    // Convert lampiran to the expected format if it's not already
    lampiran: purchaseResult.purchase.lampiran
      ? purchaseResult.purchase.lampiran.map((item: any) => {
          // If it's already in the correct format, return as is
          if (
            typeof item === "object" &&
            item !== null &&
            "url" in item &&
            "filename" in item
          ) {
            return item;
          }
          // Otherwise, try to parse it if it's a string
          if (typeof item === "string") {
            try {
              const parsed = JSON.parse(item);
              return parsed;
            } catch (e) {
              // If parsing fails, create a default object
              return { url: item, filename: "File" };
            }
          }
          // Default fallback
          return { url: "", filename: "Unknown" };
        })
      : [],
    createdAt: purchaseResult.purchase.createdAt, // Add createdAt field
    // Process items to ensure they match the PurchaseItem interface
    items: purchaseResult.purchase.items.map((item) => {
      // Find the full product from the products array
      const fullProduct = products.find((p) => p.id === item.productId);

      if (!fullProduct) {
        console.error(
          `Product with ID ${item.productId} not found in products array`
        );
      }

      return {
        id: item.id,
        quantity: item.quantity,
        costAtPurchase: item.costAtPurchase,
        productId: item.productId,
        unit: item.unit || "Buah",
        tax: item.tax || "",
        // Use the full product from the products array if available, otherwise use the item.product
        product: fullProduct || {
          ...item.product,
          // Ensure all Decimal objects are converted to numbers
          price:
            typeof item.product.price === "number"
              ? item.product.price
              : Number(item.product.price),
          cost: item.product.cost
            ? typeof item.product.cost === "number"
              ? item.product.cost
              : Number(item.product.cost)
            : null,
          weight: item.product.weight
            ? typeof item.product.weight === "number"
              ? item.product.weight
              : Number(item.product.weight)
            : null,
          length: item.product.length
            ? typeof item.product.length === "number"
              ? item.product.length
              : Number(item.product.length)
            : null,
          width: item.product.width
            ? typeof item.product.width === "number"
              ? item.product.width
              : Number(item.product.width)
            : null,
          height: item.product.height
            ? typeof item.product.height === "number"
              ? item.product.height
              : Number(item.product.height)
            : null,
          discountPrice: item.product.discountPrice
            ? typeof item.product.discountPrice === "number"
              ? item.product.discountPrice
              : Number(item.product.discountPrice)
            : null,
        },
      };
    }),
  };

  return (
    <DashboardLayout>
      <EnhancedPurchaseEditPage
        purchase={serializedPurchase}
        products={products}
        suppliers={suppliersResult.suppliers}
      />
    </DashboardLayout>
  );
}
