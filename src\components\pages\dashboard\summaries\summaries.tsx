"use client";

import React, { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Skeleton } from "@/components/ui/skeleton";
import { Terminal } from "lucide-react";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import {
  getDashboardSummary,
  getSalesChartData,
  getProductDistributionData,
  getRecentTransactions,
} from "@/actions/dashboard/dashboard";

// Import our custom components
import { OverviewTab } from "./components/OverviewTab";
import { SalesTab } from "./components/SalesTab";
import { InventoryTab } from "./components/InventoryTab";

// Define types for data
type SummaryData = {
  salesToday: number;
  salesChange: number;
  totalProducts: number;
  newProductsCount: number;
  totalCustomers: number;
  newCustomersCount: number;
  purchasesThisMonth: number;
  purchasesChange: number;
};

type SalesChartDataPoint = {
  name: string;
  total: number;
};

type ProductDistributionDataPoint = {
  name: string;
  value: number;
};

type RecentTransactionItem = {
  id: string;
  time: string;
  amount: string;
  status?: "success" | "pending" | "failed";
};

export default function SummariesPage() {
  // State for all dashboard data
  const [summaryData, setSummaryData] = useState<SummaryData | null>(null);
  const [salesChartData, setSalesChartData] = useState<
    SalesChartDataPoint[] | null
  >(null);
  const [productDistData, setProductDistData] = useState<
    ProductDistributionDataPoint[] | null
  >(null);
  const [recentTransData, setRecentTransData] = useState<
    RecentTransactionItem[] | null
  >(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchAllData = async () => {
      setIsLoading(true);
      setError(null);
      try {
        // Fetch all data concurrently
        const [
          summaryResult,
          salesChartResult,
          productDistResult,
          recentTransResult,
        ] = await Promise.all([
          getDashboardSummary(),
          getSalesChartData(),
          getProductDistributionData(),
          getRecentTransactions(),
        ]);

        // Process results and update state
        if (summaryResult.success && summaryResult.data) {
          setSummaryData(summaryResult.data);
        } else {
          throw new Error(
            summaryResult.error || "Gagal mengambil data ringkasan."
          );
        }

        if (salesChartResult.success && salesChartResult.data) {
          setSalesChartData(salesChartResult.data);
        } else {
          throw new Error(
            salesChartResult.error || "Gagal mengambil data grafik penjualan."
          );
        }

        if (productDistResult.success && productDistResult.data) {
          setProductDistData(productDistResult.data);
        } else {
          throw new Error(
            productDistResult.error || "Gagal mengambil data distribusi produk."
          );
        }

        if (recentTransResult.success && recentTransResult.data) {
          setRecentTransData(recentTransResult.data);
        } else {
          throw new Error(
            recentTransResult.error || "Gagal mengambil data transaksi terbaru."
          );
        }
      } catch (err) {
        console.error("Error fetching dashboard data:", err);
        setError(
          err instanceof Error
            ? err.message
            : "Terjadi kesalahan saat mengambil data dashboard."
        );
      } finally {
        setIsLoading(false);
      }
    };

    fetchAllData();
  }, []);

  // Loading state
  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
          {[1, 2, 3, 4].map((i) => (
            <Card key={i} className="border-none shadow-md dark:bg-gray-800">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <Skeleton className="h-4 w-24" />
                <Skeleton className="h-8 w-8 rounded-full" />
              </CardHeader>
              <CardContent>
                <Skeleton className="h-8 w-28 mb-2" />
                <Skeleton className="h-3 w-20" />
              </CardContent>
            </Card>
          ))}
        </div>

        <div className="grid grid-cols-1 gap-6 lg:grid-cols-7">
          <Card className="col-span-1 lg:col-span-4 border-none shadow-md dark:bg-gray-800">
            <CardHeader>
              <Skeleton className="h-5 w-32 mb-2" />
              <Skeleton className="h-4 w-48" />
            </CardHeader>
            <CardContent>
              <Skeleton className="h-[300px] w-full" />
            </CardContent>
          </Card>

          <Card className="col-span-1 lg:col-span-3 border-none shadow-md dark:bg-gray-800">
            <CardHeader>
              <Skeleton className="h-5 w-40 mb-2" />
              <Skeleton className="h-4 w-48" />
            </CardHeader>
            <CardContent>
              <Skeleton className="h-[300px] w-full" />
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    // Special handling for authentication errors
    if (error.includes("Tidak terautentikasi")) {
      return (
        <Alert variant="destructive">
          <Terminal className="h-4 w-4" />
          <AlertTitle>Akses Ditolak</AlertTitle>
          <AlertDescription>
            Anda perlu login untuk melihat data dashboard. Silakan login
            kembali.
          </AlertDescription>
        </Alert>
      );
    }

    return (
      <Alert variant="destructive">
        <Terminal className="h-4 w-4" />
        <AlertTitle>Error</AlertTitle>
        <AlertDescription>{error}</AlertDescription>
      </Alert>
    );
  }

  // Check if all required data is loaded
  if (!summaryData || !salesChartData || !productDistData || !recentTransData) {
    return (
      <Alert>
        <Terminal className="h-4 w-4" />
        <AlertTitle>Perhatian</AlertTitle>
        <AlertDescription>
          Data ringkasan tidak lengkap atau tidak tersedia.
        </AlertDescription>
      </Alert>
    );
  }

  return (
    <div className="space-y-6">
      <Tabs defaultValue="overview" className="w-full">
        <TabsList className="mb-4">
          <TabsTrigger value="overview">Ringkasan</TabsTrigger>
          <TabsTrigger value="sales">Penjualan</TabsTrigger>
          <TabsTrigger value="inventory">Inventaris</TabsTrigger>
        </TabsList>

        <TabsContent value="overview">
          <OverviewTab
            summaryData={summaryData}
            salesChartData={salesChartData}
            productDistData={productDistData}
            recentTransData={recentTransData}
          />
        </TabsContent>

        <TabsContent value="sales">
          <SalesTab salesChartData={salesChartData} />
        </TabsContent>

        <TabsContent value="inventory">
          <InventoryTab productDistData={productDistData} />
        </TabsContent>
      </Tabs>
    </div>
  );
}
