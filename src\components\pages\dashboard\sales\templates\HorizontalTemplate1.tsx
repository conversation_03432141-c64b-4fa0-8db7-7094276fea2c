import type { Sale } from "../types";

/**
 * Horizontal Template 1 - Based on custom1 template with horizontal orientation
 */
export const renderHorizontalTemplate1 = (sale: Sale): string => {
  return `
    <!DOCTYPE html>
    <html lang="id">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Faktur Penjualan - ${sale.transactionNumber || sale.id.substring(0, 8)}</title>
      <style>
        @page {
          size: A4 landscape;
          margin: 1.5cm;
        }
        body {
          font-family: Arial, sans-serif;
          margin: 0;
          padding: 0;
          color: #000;
          background-color: white;
          font-size: 12pt;
        }
        .invoice-container {
          max-width: 29.7cm;
          margin: 0 auto;
          padding: 20px;
          box-sizing: border-box;
        }
        .letterhead {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 20px;
          border-bottom: 2px solid #333;
          padding-bottom: 15px;
        }
        .company-info {
          flex: 2;
        }
        .company-name {
          font-size: 24pt;
          font-weight: bold;
          margin-bottom: 5px;
        }
        .company-details {
          font-size: 10pt;
        }
        .invoice-label {
          flex: 1;
          text-align: right;
        }
        .invoice-title {
          font-size: 28pt;
          font-weight: bold;
          color: #333;
        }
        .invoice-details {
          display: flex;
          justify-content: space-between;
          margin-bottom: 20px;
          font-size: 11pt;
        }
        .invoice-number {
          font-weight: bold;
        }
        .invoice-date {
          text-align: right;
        }
        .parties {
          display: flex;
          justify-content: space-between;
          margin-bottom: 15px;
        }
        .customer-info, .seller-info {
          width: 48%;
          padding: 0;
          border-bottom: 1px solid #ddd;
        }
        .section-title {
          font-weight: bold;
          font-size: 11pt;
          margin-bottom: 6px;
        }
        .info-row {
          margin-bottom: 4px;
          display: flex;
        }
        .info-label {
          font-weight: bold;
          width: 80px;
          font-size: 10pt;
        }
        .info-value {
          flex: 1;
          font-size: 10pt;
          padding-left: 10px;
        }
        table {
          width: 100%;
          border-collapse: collapse;
          margin-bottom: 20px;
        }
        th, td {
          border: 1px solid #ddd;
          padding: 10px;
          text-align: left;
          font-size: 11pt;
        }
        th {
          background-color: #f2f2f2;
          font-weight: bold;
        }
        .text-right {
          text-align: right;
        }
        .text-center {
          text-align: center;
        }
        .total-section {
          margin-top: 20px;
          text-align: right;
        }
        .total-row {
          font-weight: bold;
          font-size: 12pt;
        }
        .signatures {
          display: flex;
          justify-content: space-between;
          margin-top: 50px;
          page-break-inside: avoid;
        }
        .signature-box {
          width: 45%;
          text-align: center;
        }
        .signature-line {
          border-bottom: 1px solid #000;
          margin-bottom: 10px;
          height: 70px;
        }
        .signature-name {
          font-weight: bold;
        }
        .signature-title {
          font-size: 10pt;
        }
        .footer {
          margin-top: 30px;
          text-align: center;
          font-size: 10pt;
          color: #555;
          border-top: 1px solid #ddd;
          padding-top: 10px;
          page-break-inside: avoid;
        }
        .memo {
          margin-top: 20px;
          border: 1px solid #ddd;
          padding: 10px;
          font-size: 10pt;
          page-break-inside: avoid;
          background-color: #f9f9f9;
          border-radius: 5px;
        }
        .memo-title {
          font-weight: bold;
          margin-bottom: 5px;
        }
        @media print {
          body {
            -webkit-print-color-adjust: exact;
            print-color-adjust: exact;
          }
          .invoice-container {
            padding: 0;
          }
          .no-print {
            display: none;
          }
        }
      </style>
    </head>
    <body>
      <div class="invoice-container">
        <!-- Letterhead -->
        <div class="letterhead">
          <div class="company-info">
            <div class="company-name">${sale.user?.name || "Kasir Online"}</div>
            <div class="company-details">
              ${sale.shippingAddress || "Alamat belum diatur"}<br>
              Telp: - | Email: -
            </div>
            <div class="seller-info" style="margin-top: 10px;">
              <div class="section-title">Penjual</div>
              <div class="info-row">
                <div class="info-label">Nama</div>
                <div class="info-value">: ${sale.user?.name || "Kasir Online"}</div>
              </div>
              <div class="info-row">
                <div class="info-label">ID</div>
                <div class="info-value">: -</div>
              </div>
            </div>
          </div>
          <div class="invoice-label">
            <div class="invoice-title">FAKTUR</div>
          </div>
        </div>

        <!-- Invoice Details and Customer Info -->
        <div class="parties">
          <div class="customer-info" style="width: 48%;">
            <div class="section-title">Pelanggan</div>
            <div class="info-row">
              <div class="info-label">Nama</div>
              <div class="info-value">: ${sale.customer ? sale.customer.name : "-"}</div>
            </div>
            <div class="info-row">
              <div class="info-label">Email</div>
              <div class="info-value">: ${sale.customerEmail || "-"}</div>
            </div>
            <div class="info-row">
              <div class="info-label">Alamat</div>
              <div class="info-value">: ${sale.shippingAddress || "-"}</div>
            </div>
          </div>

          <div class="customer-info" style="width: 48%;">
            <div class="section-title">Informasi Faktur</div>
            <div class="info-row">
              <div class="info-label">No. Faktur</div>
              <div class="info-value">: ${sale.transactionNumber || sale.id.substring(0, 8)}</div>
            </div>
            <div class="info-row">
              <div class="info-label">No. Ref</div>
              <div class="info-value">: ${sale.invoiceRef || "-"}</div>
            </div>
            <div class="info-row">
              <div class="info-label">Tanggal</div>
              <div class="info-value">: ${new Date(
                sale.saleDate
              ).toLocaleDateString("id-ID", {
                day: "numeric",
                month: "long",
                year: "numeric",
              })}</div>
            </div>
            <div class="info-row">
              <div class="info-label">Jth Tempo</div>
              <div class="info-value">: ${
                sale.paymentDueDate
                  ? new Date(sale.paymentDueDate).toLocaleDateString("id-ID", {
                      day: "numeric",
                      month: "long",
                      year: "numeric",
                    })
                  : "-"
              }</div>
            </div>
          </div>
        </div>

        <!-- Items Table -->
        <table>
          <thead>
            <tr>
              <th class="text-center" style="width: 5%;">No.</th>
              <th style="width: 40%;">Nama Barang</th>
              <th class="text-center" style="width: 10%;">Quantity</th>
              <th class="text-center" style="width: 10%;">Satuan</th>
              <th class="text-right" style="width: 15%;">Harga Satuan</th>
              <th class="text-right" style="width: 20%;">Total</th>
            </tr>
          </thead>
          <tbody>
            ${sale.items
              .map(
                (item, index) => `
              <tr>
                <td class="text-center">${index + 1}</td>
                <td>${item.product.name}</td>
                <td class="text-center">${item.quantity}</td>
                <td class="text-center">Buah</td>
                <td class="text-right">Rp ${item.priceAtSale.toLocaleString("id-ID")}</td>
                <td class="text-right">Rp ${(item.quantity * item.priceAtSale).toLocaleString("id-ID")}</td>
              </tr>
            `
              )
              .join("")}
          </tbody>
        </table>

        <!-- Summary Section -->
        <table style="border-collapse: collapse; margin-top: -20px; width: 100%;">
          <tr>
            <td colspan="5" style="border: none; text-align: right; padding: 4px 10px; line-height: 1.2;">Sub-Total:</td>
            <td style="border: none; text-align: right; padding: 4px 10px; width: 20%; line-height: 1.2;">Rp ${sale.totalAmount.toLocaleString("id-ID")}</td>
          </tr>
          <tr>
            <td colspan="5" style="border: none; text-align: right; padding: 4px 10px; line-height: 1.2;">Diskon:</td>
            <td style="border: none; text-align: right; padding: 4px 10px; line-height: 1.2;">-</td>
          </tr>
          <tr>
            <td colspan="5" style="border: none; text-align: right; padding: 4px 10px; line-height: 1.2;">PPN:</td>
            <td style="border: none; text-align: right; padding: 4px 10px; line-height: 1.2;">-</td>
          </tr>
          <tr style="font-weight: bold;">
            <td colspan="5" style="border: none; text-align: right; padding: 4px 10px; line-height: 1.2;">Total:</td>
            <td style="border: none; text-align: right; padding: 4px 10px; line-height: 1.2;">Rp ${sale.totalAmount.toLocaleString("id-ID")}</td>
          </tr>
        </table>

        <!-- Memo Section (if available) -->
        ${
          sale.memo
            ? `
        <div class="memo">
          <div class="memo-title">Catatan:</div>
          <div>${sale.memo}</div>
        </div>
        `
            : ""
        }

        <!-- Signatures -->
        <div class="signatures">
          <div class="signature-box">
            <div class="signature-line"></div>
            <div class="signature-name">( Kasir Online )</div>
            <div class="signature-title">Penjual</div>
          </div>
          <div class="signature-box">
            <div class="signature-line"></div>
            <div class="signature-name">( ${sale.customer ? sale.customer.name : "Pelanggan"} )</div>
            <div class="signature-title">Pembeli</div>
          </div>
        </div>

        <!-- Footer -->
        <div class="footer">
          <p>Faktur ini adalah bukti resmi penjualan. Terima kasih atas kerjasamanya.</p>
          <p>Dicetak melalui Kasir Online pada ${new Date().toLocaleDateString(
            "id-ID",
            {
              day: "numeric",
              month: "long",
              year: "numeric",
            }
          )}</p>
        </div>
      </div>
    </body>
    </html>
  `;
};
