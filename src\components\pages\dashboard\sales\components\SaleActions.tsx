import React, { useState } from "react";
import Link from "next/link";
import {
  MagnifyingGlassIcon,
  PlusIcon,
  FunnelIcon,
  ArrowDownTrayIcon,
  ArrowUpTrayIcon,
  AdjustmentsHorizontalIcon,
  TrashIcon,
} from "@heroicons/react/24/outline";
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

interface ColumnVisibility {
  id: boolean;
  date: boolean;
  paymentDueDate: boolean;
  customer: boolean;
  totalAmount: boolean;
  itemCount: boolean;
  invoiceRef: boolean;
  tags: boolean;
}

interface SaleActionsProps {
  columnVisibility: ColumnVisibility;
  setColumnVisibility: React.Dispatch<React.SetStateAction<ColumnVisibility>>;
  searchTerm: string;
  setSearchTerm: (term: string) => void;
  onFilterClick: () => void;
  onImportClick: () => void;
  onExportClick: () => void;
  selectedSales?: string[];
  onBatchDelete?: () => Promise<void>;
}

export const SaleActions: React.FC<SaleActionsProps> = ({
  columnVisibility,
  setColumnVisibility,
  searchTerm,
  setSearchTerm,
  onFilterClick,
  onImportClick,
  onExportClick,
  selectedSales = [],
  onBatchDelete,
}) => {
  const [isDeleting, setIsDeleting] = useState(false);

  const handleBatchDelete = async () => {
    if (!onBatchDelete) return;
    setIsDeleting(true);
    try {
      await onBatchDelete();
    } finally {
      setIsDeleting(false);
    }
  };
  return (
    <div className="flex flex-wrap items-center justify-between gap-4">
      <div className="flex flex-wrap items-center gap-2">
        {/* Column Visibility Dropdown */}
        <DropdownMenu>
          <DropdownMenuTrigger className="inline-flex items-center justify-center rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 px-3 py-2 text-sm font-medium text-gray-700 dark:text-gray-200 shadow-sm hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 cursor-pointer">
            <AdjustmentsHorizontalIcon className="mr-2 h-5 w-5" />
            Kolom
          </DropdownMenuTrigger>
          <DropdownMenuContent
            align="end"
            className="w-56"
            onCloseAutoFocus={(e) => e.preventDefault()}
          >
            <DropdownMenuLabel>Tampilkan Kolom</DropdownMenuLabel>
            <DropdownMenuSeparator />
            <DropdownMenuCheckboxItem
              checked={columnVisibility.id}
              onCheckedChange={(checked) =>
                setColumnVisibility((prev) => ({ ...prev, id: !!checked }))
              }
              onSelect={(e) => e.preventDefault()}
            >
              No. Transaksi
            </DropdownMenuCheckboxItem>
            <DropdownMenuCheckboxItem
              checked={columnVisibility.date}
              onCheckedChange={(checked) =>
                setColumnVisibility((prev) => ({ ...prev, date: !!checked }))
              }
              onSelect={(e) => e.preventDefault()}
            >
              Tanggal
            </DropdownMenuCheckboxItem>
            <DropdownMenuCheckboxItem
              checked={columnVisibility.paymentDueDate}
              onCheckedChange={(checked) =>
                setColumnVisibility((prev) => ({
                  ...prev,
                  paymentDueDate: !!checked,
                }))
              }
              onSelect={(e) => e.preventDefault()}
            >
              Tgl. Jatuh Tempo
            </DropdownMenuCheckboxItem>
            <DropdownMenuCheckboxItem
              checked={columnVisibility.customer}
              onCheckedChange={(checked) =>
                setColumnVisibility((prev) => ({
                  ...prev,
                  customer: !!checked,
                }))
              }
              onSelect={(e) => e.preventDefault()}
            >
              Pelanggan
            </DropdownMenuCheckboxItem>
            <DropdownMenuCheckboxItem
              checked={columnVisibility.totalAmount}
              onCheckedChange={(checked) =>
                setColumnVisibility((prev) => ({
                  ...prev,
                  totalAmount: !!checked,
                }))
              }
              onSelect={(e) => e.preventDefault()}
            >
              Total
            </DropdownMenuCheckboxItem>
            <DropdownMenuCheckboxItem
              checked={columnVisibility.itemCount}
              onCheckedChange={(checked) =>
                setColumnVisibility((prev) => ({
                  ...prev,
                  itemCount: !!checked,
                }))
              }
              onSelect={(e) => e.preventDefault()}
            >
              Jumlah Item
            </DropdownMenuCheckboxItem>
            <DropdownMenuCheckboxItem
              checked={columnVisibility.invoiceRef}
              onCheckedChange={(checked) =>
                setColumnVisibility((prev) => ({
                  ...prev,
                  invoiceRef: !!checked,
                }))
              }
              onSelect={(e) => e.preventDefault()}
            >
              No. Faktur
            </DropdownMenuCheckboxItem>
            <DropdownMenuCheckboxItem
              checked={columnVisibility.tags}
              onCheckedChange={(checked) =>
                setColumnVisibility((prev) => ({
                  ...prev,
                  tags: !!checked,
                }))
              }
              onSelect={(e) => e.preventDefault()}
            >
              Tag
            </DropdownMenuCheckboxItem>
          </DropdownMenuContent>
        </DropdownMenu>

        {/* Filter Button */}
        <button
          type="button"
          onClick={onFilterClick}
          className="inline-flex items-center justify-center rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 px-3 py-2 text-sm font-medium text-gray-700 dark:text-gray-200 shadow-sm hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 cursor-pointer"
        >
          <FunnelIcon className="mr-2 h-5 w-5" />
          Filter
        </button>

        {/* Import Button */}
        <button
          type="button"
          onClick={onImportClick}
          className="inline-flex items-center justify-center rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 px-3 py-2 text-sm font-medium text-gray-700 dark:text-gray-200 shadow-sm hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 cursor-pointer"
        >
          <ArrowDownTrayIcon className="mr-2 h-5 w-5" />
          Import
        </button>

        {/* Export Button */}
        <button
          type="button"
          onClick={onExportClick}
          className="inline-flex items-center justify-center rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 px-3 py-2 text-sm font-medium text-gray-700 dark:text-gray-200 shadow-sm hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 cursor-pointer"
        >
          <ArrowUpTrayIcon className="mr-2 h-5 w-5" />
          Export
        </button>

        {/* Batch Delete Button - Only show when items are selected */}
        {selectedSales.length > 0 && (
          <button
            type="button"
            onClick={handleBatchDelete}
            disabled={isDeleting}
            className="inline-flex items-center justify-center rounded-md border border-transparent bg-red-600 px-3 py-2 text-sm font-medium text-white shadow-sm hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 cursor-pointer"
          >
            <TrashIcon className="mr-2 h-5 w-5" />
            {isDeleting ? "Menghapus..." : `Hapus (${selectedSales.length})`}
          </button>
        )}
      </div>

      <div className="flex flex-wrap items-center gap-2">
        {/* Search Input */}
        <div className="relative">
          <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
            <MagnifyingGlassIcon
              className="h-5 w-5 text-gray-400"
              aria-hidden="true"
            />
          </div>
          <input
            type="text"
            placeholder="Cari transaksi..."
            className="block w-full rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 py-2 pl-10 pr-3 text-sm placeholder-gray-500 focus:border-indigo-500 focus:outline-none focus:ring-indigo-500 dark:text-gray-100"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>

        {/* Add Sale Button */}
        <Link
          href="/dashboard/sales/new"
          className="inline-flex items-center justify-center rounded-md border border-transparent bg-indigo-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 cursor-pointer"
        >
          <PlusIcon className="mr-2 h-5 w-5" />
          Tambah
        </Link>
      </div>
    </div>
  );
};
