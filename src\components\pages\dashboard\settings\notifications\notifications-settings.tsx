"use client";

import { useState, useEffect } from "react";
import {
  Bell,
  AlertCircle,
  Mail,
  BarChart,
  Smartphone,
  AlertTriangle,
  Info,
  Send,
} from "lucide-react";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Switch } from "@/components/ui/switch";
import { Button } from "@/components/ui/button";
import { toast } from "sonner";
import { Skeleton } from "@/components/ui/skeleton";
import {
  getNotificationSettings,
  updateNotificationSettings,
  sendTestNotificationEmail,
  type NotificationSettingsType,
} from "@/actions/notifications/settings";

export default function NotificationsSettings() {
  // State for notification settings
  const [settings, setSettings] = useState<NotificationSettingsType>({
    emailEnabled: true,
    emailInfoEnabled: true,
    emailWarningEnabled: true,
    emailSuccessEnabled: true,
    emailErrorEnabled: true,
    dailySummary: false,
    weeklySummary: true,
  });

  // UI state
  const [showError, setShowError] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [isFetching, setIsFetching] = useState(true);
  const [isSendingTest, setIsSendingTest] = useState(false);

  // Fetch notification settings on component mount
  useEffect(() => {
    async function fetchSettings() {
      setIsFetching(true);
      try {
        const result = await getNotificationSettings();
        if (result.success && result.data) {
          setSettings(result.data);
        } else {
          setErrorMessage(result.error || "Gagal memuat pengaturan notifikasi");
          setShowError(true);
        }
      } catch (error) {
        console.error("Error fetching notification settings:", error);
        setErrorMessage("Terjadi kesalahan saat memuat pengaturan");
        setShowError(true);
      } finally {
        setIsFetching(false);
      }
    }

    fetchSettings();
  }, []);

  // Handle saving settings
  const handleSaveSettings = async () => {
    setIsLoading(true);
    setShowError(false);

    try {
      const result = await updateNotificationSettings(settings);

      if (result.success) {
        toast.success("Pengaturan notifikasi berhasil disimpan!");
      } else {
        setErrorMessage(
          result.error || "Terjadi kesalahan saat menyimpan pengaturan"
        );
        setShowError(true);
        toast.error("Terjadi kesalahan saat menyimpan pengaturan.");
      }
    } catch (error) {
      console.error("Error saving notification settings:", error);
      setErrorMessage("Terjadi kesalahan saat menyimpan pengaturan");
      setShowError(true);
      toast.error("Terjadi kesalahan saat menyimpan pengaturan.");
    } finally {
      setIsLoading(false);
    }
  };

  // Handle sending test email
  const handleSendTestEmail = async () => {
    setIsSendingTest(true);

    try {
      const result = await sendTestNotificationEmail();

      if (result.success) {
        toast.success("Email uji coba berhasil dikirim!");
      } else {
        toast.error(result.error || "Gagal mengirim email uji coba");
      }
    } catch (error) {
      console.error("Error sending test email:", error);
      toast.error("Terjadi kesalahan saat mengirim email uji coba");
    } finally {
      setIsSendingTest(false);
    }
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader className="pb-3 flex flex-row items-center space-y-0 gap-4">
          <div className="h-12 w-12 rounded-full bg-primary/10 flex items-center justify-center">
            <Bell className="h-6 w-6 text-primary" />
          </div>
          <div>
            <CardTitle className="text-xl">Pengaturan Notifikasi</CardTitle>
            <CardDescription>
              Atur preferensi notifikasi dan pemberitahuan
            </CardDescription>
          </div>
        </CardHeader>

        <CardContent>
          <Tabs defaultValue="email" className="w-full">
            <TabsList className="grid w-full grid-cols-3 mb-6">
              <TabsTrigger value="email" className="flex items-center gap-2">
                <Mail className="h-4 w-4" />
                <span>Email</span>
              </TabsTrigger>
              <TabsTrigger value="summary" className="flex items-center gap-2">
                <BarChart className="h-4 w-4" />
                <span>Ringkasan</span>
              </TabsTrigger>
              <TabsTrigger value="push" className="flex items-center gap-2">
                <Smartphone className="h-4 w-4" />
                <span>Push</span>
              </TabsTrigger>
            </TabsList>

            <TabsContent value="email" className="space-y-6">
              <div className="space-y-4">
                <div className="flex items-center justify-between p-4 bg-card rounded-lg border shadow-sm hover:shadow-md transition-shadow">
                  <div className="flex items-start gap-3">
                    <div className="mt-0.5 p-1.5 rounded-md bg-blue-100 dark:bg-blue-900/20">
                      <Mail className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                    </div>
                    <div>
                      <h4 className="text-sm font-medium">
                        Aktifkan Notifikasi Email
                      </h4>
                      <p className="text-xs text-muted-foreground mt-1">
                        Terima semua notifikasi melalui email
                      </p>
                    </div>
                  </div>
                  {isFetching ? (
                    <Skeleton className="h-6 w-11" />
                  ) : (
                    <Switch
                      checked={settings.emailEnabled}
                      onCheckedChange={(checked) =>
                        setSettings({ ...settings, emailEnabled: checked })
                      }
                    />
                  )}
                </div>

                {settings.emailEnabled && !isFetching && (
                  <div className="space-y-3 mt-4 pl-4 border-l-2 border-primary/20">
                    <div className="flex items-center justify-between p-3 rounded-lg bg-card border shadow-sm hover:shadow-md transition-shadow">
                      <div className="flex items-start gap-3">
                        <div className="mt-0.5 p-1.5 rounded-md bg-green-100 dark:bg-green-900/20">
                          <Info className="h-4 w-4 text-green-600 dark:text-green-400" />
                        </div>
                        <div>
                          <h4 className="text-sm font-medium">
                            Pemberitahuan Penjualan
                          </h4>
                          <p className="text-xs text-muted-foreground mt-1">
                            Notifikasi saat ada penjualan baru
                          </p>
                        </div>
                      </div>
                      <Switch
                        checked={settings.emailSuccessEnabled}
                        onCheckedChange={(checked) =>
                          setSettings({
                            ...settings,
                            emailSuccessEnabled: checked,
                          })
                        }
                      />
                    </div>

                    <div className="flex items-center justify-between p-3 rounded-lg bg-card border shadow-sm hover:shadow-md transition-shadow">
                      <div className="flex items-start gap-3">
                        <div className="mt-0.5 p-1.5 rounded-md bg-amber-100 dark:bg-amber-900/20">
                          <AlertTriangle className="h-4 w-4 text-amber-600 dark:text-amber-400" />
                        </div>
                        <div>
                          <h4 className="text-sm font-medium">
                            Peringatan Inventaris
                          </h4>
                          <p className="text-xs text-muted-foreground mt-1">
                            Notifikasi saat stok produk hampir habis
                          </p>
                        </div>
                      </div>
                      <Switch
                        checked={settings.emailWarningEnabled}
                        onCheckedChange={(checked) =>
                          setSettings({
                            ...settings,
                            emailWarningEnabled: checked,
                          })
                        }
                      />
                    </div>

                    <div className="flex items-center justify-between p-3 rounded-lg bg-card border shadow-sm hover:shadow-md transition-shadow">
                      <div className="flex items-start gap-3">
                        <div className="mt-0.5 p-1.5 rounded-md bg-purple-100 dark:bg-purple-900/20">
                          <Mail className="h-4 w-4 text-purple-600 dark:text-purple-400" />
                        </div>
                        <div>
                          <h4 className="text-sm font-medium">
                            Email Pemasaran
                          </h4>
                          <p className="text-xs text-muted-foreground mt-1">
                            Terima pembaruan fitur dan penawaran khusus
                          </p>
                        </div>
                      </div>
                      <Switch
                        checked={settings.emailInfoEnabled}
                        onCheckedChange={(checked) =>
                          setSettings({
                            ...settings,
                            emailInfoEnabled: checked,
                          })
                        }
                      />
                    </div>
                  </div>
                )}
              </div>
            </TabsContent>

            <TabsContent value="summary" className="space-y-6">
              <div className="space-y-4">
                <div className="flex items-center justify-between p-4 bg-card rounded-lg border shadow-sm hover:shadow-md transition-shadow">
                  <div className="flex items-start gap-3">
                    <div className="mt-0.5 p-1.5 rounded-md bg-indigo-100 dark:bg-indigo-900/20">
                      <BarChart className="h-4 w-4 text-indigo-600 dark:text-indigo-400" />
                    </div>
                    <div>
                      <h4 className="text-sm font-medium">Ringkasan Harian</h4>
                      <p className="text-xs text-muted-foreground mt-1">
                        Terima laporan ringkasan aktivitas harian
                      </p>
                    </div>
                  </div>
                  {isFetching ? (
                    <Skeleton className="h-6 w-11" />
                  ) : (
                    <Switch
                      checked={settings.dailySummary}
                      onCheckedChange={(checked) =>
                        setSettings({ ...settings, dailySummary: checked })
                      }
                    />
                  )}
                </div>

                <div className="flex items-center justify-between p-4 bg-card rounded-lg border shadow-sm hover:shadow-md transition-shadow">
                  <div className="flex items-start gap-3">
                    <div className="mt-0.5 p-1.5 rounded-md bg-indigo-100 dark:bg-indigo-900/20">
                      <BarChart className="h-4 w-4 text-indigo-600 dark:text-indigo-400" />
                    </div>
                    <div>
                      <h4 className="text-sm font-medium">
                        Ringkasan Mingguan
                      </h4>
                      <p className="text-xs text-muted-foreground mt-1">
                        Terima laporan ringkasan aktivitas mingguan
                      </p>
                    </div>
                  </div>
                  {isFetching ? (
                    <Skeleton className="h-6 w-11" />
                  ) : (
                    <Switch
                      checked={settings.weeklySummary}
                      onCheckedChange={(checked) =>
                        setSettings({ ...settings, weeklySummary: checked })
                      }
                    />
                  )}
                </div>
              </div>
            </TabsContent>

            <TabsContent value="push" className="space-y-6">
              <div className="space-y-4">
                <div className="p-6 bg-gradient-to-br from-primary/5 to-primary/10 rounded-lg border border-primary/20 shadow-sm">
                  <div className="flex items-center gap-4 mb-4">
                    <div className="h-10 w-10 rounded-full bg-primary/20 flex items-center justify-center">
                      <Smartphone className="h-5 w-5 text-primary" />
                    </div>
                    <div>
                      <h4 className="text-base font-medium">Segera Hadir</h4>
                      <p className="text-sm text-muted-foreground">
                        Fitur notifikasi push akan segera tersedia
                      </p>
                    </div>
                  </div>
                  <p className="text-sm mb-6">
                    Kami sedang mengembangkan fitur notifikasi push untuk
                    aplikasi web dan mobile. Fitur ini akan tersedia dalam
                    pembaruan mendatang.
                  </p>

                  <div className="border-t border-primary/10 pt-6">
                    <h4 className="text-base font-medium mb-3">
                      Uji Notifikasi Email
                    </h4>
                    <p className="text-sm mb-4">
                      Kirim email uji coba untuk memastikan pengaturan
                      notifikasi email Anda berfungsi dengan baik.
                    </p>
                    <Button
                      variant="outline"
                      onClick={handleSendTestEmail}
                      disabled={isSendingTest || !settings.emailEnabled}
                      className="flex items-center gap-2"
                    >
                      <Send className="h-4 w-4" />
                      {isSendingTest ? "Mengirim..." : "Kirim Email Uji Coba"}
                    </Button>
                    {!settings.emailEnabled && (
                      <p className="text-xs text-muted-foreground mt-2">
                        Aktifkan notifikasi email terlebih dahulu untuk mengirim
                        email uji coba.
                      </p>
                    )}
                  </div>
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>

        <CardFooter className="flex justify-end border-t pt-6">
          <Button
            onClick={handleSaveSettings}
            disabled={isLoading || isFetching}
            className="px-6"
          >
            {isLoading ? "Menyimpan..." : "Simpan Perubahan"}
          </Button>
        </CardFooter>
      </Card>

      {showError && (
        <Card className="border-destructive">
          <CardContent className="pt-6">
            <div className="flex items-center gap-3 text-destructive">
              <AlertCircle className="h-5 w-5" />
              <p className="text-sm font-medium">
                {errorMessage || "Terjadi kesalahan saat menyimpan pengaturan."}
              </p>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
