"use client";

import { useState } from "react";
import { HelpCircle, Mail, MessageSquare, Phone, FileText, ExternalLink } from "lucide-react";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Textarea } from "@/components/ui/textarea";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { toast } from "sonner";

export default function SupportSettings() {
  const [subject, setSubject] = useState("");
  const [message, setMessage] = useState("");
  const [category, setCategory] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmitTicket = (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    
    // Simulate API call
    setTimeout(() => {
      toast.success("Tiket dukungan berhasil dikirim!");
      setSubject("");
      setMessage("");
      setCategory("");
      setIsSubmitting(false);
    }, 1500);
  };

  return (
    <div>
      <div className="px-6 py-5 border-b border-gray-100 dark:border-gray-700 flex items-center gap-4 bg-gradient-to-r from-gray-50 to-white dark:from-gray-800 dark:to-gray-750">
        <HelpCircle className="h-7 w-7 text-indigo-600 dark:text-indigo-400" />
        <div>
          <h2 className="text-xl font-bold text-gray-900 dark:text-gray-100">
            Pusat Dukungan
          </h2>
          <p className="mt-1 text-sm text-gray-600 dark:text-gray-400">
            Dapatkan bantuan dan dukungan untuk aplikasi Kasir Online
          </p>
        </div>
      </div>

      <div className="p-6 space-y-8">
        <Tabs defaultValue="contact" className="w-full">
          <TabsList className="grid grid-cols-3 mb-6">
            <TabsTrigger value="contact" className="flex items-center gap-2">
              <MessageSquare className="h-4 w-4" />
              <span>Hubungi Kami</span>
            </TabsTrigger>
            <TabsTrigger value="faq" className="flex items-center gap-2">
              <FileText className="h-4 w-4" />
              <span>FAQ</span>
            </TabsTrigger>
            <TabsTrigger value="resources" className="flex items-center gap-2">
              <ExternalLink className="h-4 w-4" />
              <span>Sumber Daya</span>
            </TabsTrigger>
          </TabsList>

          {/* Contact Tab */}
          <TabsContent value="contact" className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {/* Contact Methods */}
              <div className="md:col-span-1 space-y-4">
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-base">Metode Kontak</CardTitle>
                    <CardDescription>Pilih cara untuk menghubungi kami</CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="flex items-center gap-3 p-3 border rounded-md">
                      <Mail className="h-5 w-5 text-indigo-600" />
                      <div>
                        <p className="font-medium">Email</p>
                        <p className="text-sm text-muted-foreground"><EMAIL></p>
                      </div>
                    </div>
                    
                    <div className="flex items-center gap-3 p-3 border rounded-md">
                      <Phone className="h-5 w-5 text-indigo-600" />
                      <div>
                        <p className="font-medium">Telepon</p>
                        <p className="text-sm text-muted-foreground">+62 812-3456-7890</p>
                        <p className="text-xs text-muted-foreground">Senin - Jumat, 09:00 - 17:00 WIB</p>
                      </div>
                    </div>
                    
                    <div className="flex items-center gap-3 p-3 border rounded-md">
                      <MessageSquare className="h-5 w-5 text-indigo-600" />
                      <div>
                        <p className="font-medium">Live Chat</p>
                        <p className="text-sm text-muted-foreground">Tersedia 24/7</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Support Ticket Form */}
              <div className="md:col-span-2">
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle>Kirim Tiket Dukungan</CardTitle>
                    <CardDescription>
                      Jelaskan masalah Anda dan tim dukungan kami akan membantu Anda
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <form onSubmit={handleSubmitTicket} className="space-y-4">
                      <div className="space-y-2">
                        <Label htmlFor="category">Kategori</Label>
                        <Select value={category} onValueChange={setCategory} required>
                          <SelectTrigger id="category">
                            <SelectValue placeholder="Pilih kategori" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="account">Akun & Profil</SelectItem>
                            <SelectItem value="billing">Tagihan & Pembayaran</SelectItem>
                            <SelectItem value="technical">Masalah Teknis</SelectItem>
                            <SelectItem value="feature">Permintaan Fitur</SelectItem>
                            <SelectItem value="other">Lainnya</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      
                      <div className="space-y-2">
                        <Label htmlFor="subject">Subjek</Label>
                        <Input
                          id="subject"
                          placeholder="Masukkan subjek tiket"
                          value={subject}
                          onChange={(e) => setSubject(e.target.value)}
                          required
                        />
                      </div>
                      
                      <div className="space-y-2">
                        <Label htmlFor="message">Pesan</Label>
                        <Textarea
                          id="message"
                          placeholder="Jelaskan masalah Anda secara detail"
                          rows={5}
                          value={message}
                          onChange={(e) => setMessage(e.target.value)}
                          required
                        />
                      </div>
                      
                      <Button type="submit" className="w-full" disabled={isSubmitting}>
                        {isSubmitting ? "Mengirim..." : "Kirim Tiket"}
                      </Button>
                    </form>
                  </CardContent>
                </Card>
              </div>
            </div>
          </TabsContent>

          {/* FAQ Tab */}
          <TabsContent value="faq" className="space-y-6">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle>Pertanyaan Umum</CardTitle>
                <CardDescription>
                  Jawaban untuk pertanyaan yang sering diajukan
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-4">
                  <div className="border rounded-md p-4 space-y-2">
                    <h3 className="font-medium">Bagaimana cara mengubah paket langganan?</h3>
                    <p className="text-sm text-muted-foreground">
                      Anda dapat mengubah paket langganan melalui halaman Pengaturan &gt; Billing. Pilih paket yang diinginkan dan ikuti petunjuk pembayaran.
                    </p>
                  </div>
                  
                  <div className="border rounded-md p-4 space-y-2">
                    <h3 className="font-medium">Bagaimana cara menambahkan karyawan?</h3>
                    <p className="text-sm text-muted-foreground">
                      Anda dapat menambahkan karyawan melalui halaman Pengaturan &gt; Karyawan. Klik tombol "Tambah Karyawan" dan isi informasi yang diperlukan.
                    </p>
                  </div>
                  
                  <div className="border rounded-md p-4 space-y-2">
                    <h3 className="font-medium">Apakah saya bisa menggunakan aplikasi ini secara offline?</h3>
                    <p className="text-sm text-muted-foreground">
                      Ya, aplikasi Kasir Online memiliki fitur offline mode yang memungkinkan Anda tetap menggunakan aplikasi meskipun tidak ada koneksi internet. Data akan disinkronkan saat koneksi internet tersedia kembali.
                    </p>
                  </div>
                  
                  <div className="border rounded-md p-4 space-y-2">
                    <h3 className="font-medium">Bagaimana cara mengekspor laporan penjualan?</h3>
                    <p className="text-sm text-muted-foreground">
                      Anda dapat mengekspor laporan penjualan melalui halaman Laporan. Pilih periode laporan yang diinginkan, kemudian klik tombol "Ekspor" dan pilih format yang diinginkan (PDF, Excel, atau CSV).
                    </p>
                  </div>
                  
                  <div className="border rounded-md p-4 space-y-2">
                    <h3 className="font-medium">Apakah aplikasi ini mendukung printer thermal?</h3>
                    <p className="text-sm text-muted-foreground">
                      Ya, aplikasi Kasir Online mendukung berbagai jenis printer thermal yang umum digunakan untuk mencetak struk. Anda dapat mengatur printer melalui halaman Pengaturan &gt; Perangkat.
                    </p>
                  </div>
                </div>
              </CardContent>
              <CardFooter>
                <Button variant="outline" className="w-full">
                  Lihat Semua FAQ
                </Button>
              </CardFooter>
            </Card>
          </TabsContent>

          {/* Resources Tab */}
          <TabsContent value="resources" className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle>Panduan Pengguna</CardTitle>
                  <CardDescription>
                    Pelajari cara menggunakan aplikasi Kasir Online
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <FileText className="h-4 w-4 text-indigo-600" />
                      <p className="font-medium">Panduan Memulai</p>
                    </div>
                    <p className="text-sm text-muted-foreground">
                      Panduan langkah demi langkah untuk memulai menggunakan aplikasi Kasir Online.
                    </p>
                    <Button variant="link" className="h-auto p-0">Baca Panduan</Button>
                  </div>
                  
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <FileText className="h-4 w-4 text-indigo-600" />
                      <p className="font-medium">Panduan Fitur Lanjutan</p>
                    </div>
                    <p className="text-sm text-muted-foreground">
                      Pelajari fitur-fitur lanjutan untuk mengoptimalkan penggunaan aplikasi.
                    </p>
                    <Button variant="link" className="h-auto p-0">Baca Panduan</Button>
                  </div>
                </CardContent>
              </Card>
              
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle>Video Tutorial</CardTitle>
                  <CardDescription>
                    Pelajari melalui video tutorial
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <ExternalLink className="h-4 w-4 text-indigo-600" />
                      <p className="font-medium">Pengenalan Aplikasi</p>
                    </div>
                    <p className="text-sm text-muted-foreground">
                      Video pengenalan aplikasi Kasir Online dan fitur-fiturnya.
                    </p>
                    <Button variant="link" className="h-auto p-0">Tonton Video</Button>
                  </div>
                  
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <ExternalLink className="h-4 w-4 text-indigo-600" />
                      <p className="font-medium">Tutorial Transaksi</p>
                    </div>
                    <p className="text-sm text-muted-foreground">
                      Pelajari cara melakukan transaksi penjualan dan pembelian.
                    </p>
                    <Button variant="link" className="h-auto p-0">Tonton Video</Button>
                  </div>
                </CardContent>
              </Card>
            </div>
            
            <Card>
              <CardHeader className="pb-2">
                <CardTitle>Artikel & Blog</CardTitle>
                <CardDescription>
                  Artikel terbaru tentang tips dan trik menggunakan aplikasi
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="border-b pb-4">
                    <h3 className="font-medium">5 Cara Meningkatkan Efisiensi Kasir</h3>
                    <p className="text-sm text-muted-foreground mt-1">
                      Pelajari cara meningkatkan efisiensi operasional kasir Anda dengan aplikasi Kasir Online.
                    </p>
                    <div className="flex items-center gap-2 mt-2">
                      <Button variant="link" className="h-auto p-0">Baca Artikel</Button>
                      <p className="text-xs text-muted-foreground">5 menit membaca</p>
                    </div>
                  </div>
                  
                  <div className="border-b pb-4">
                    <h3 className="font-medium">Cara Menganalisis Laporan Penjualan</h3>
                    <p className="text-sm text-muted-foreground mt-1">
                      Panduan lengkap untuk menganalisis laporan penjualan dan mengambil keputusan bisnis.
                    </p>
                    <div className="flex items-center gap-2 mt-2">
                      <Button variant="link" className="h-auto p-0">Baca Artikel</Button>
                      <p className="text-xs text-muted-foreground">7 menit membaca</p>
                    </div>
                  </div>
                  
                  <div>
                    <h3 className="font-medium">Tips Mengelola Inventaris dengan Efektif</h3>
                    <p className="text-sm text-muted-foreground mt-1">
                      Pelajari cara mengelola inventaris dengan efektif menggunakan fitur manajemen stok.
                    </p>
                    <div className="flex items-center gap-2 mt-2">
                      <Button variant="link" className="h-auto p-0">Baca Artikel</Button>
                      <p className="text-xs text-muted-foreground">6 menit membaca</p>
                    </div>
                  </div>
                </div>
              </CardContent>
              <CardFooter>
                <Button variant="outline" className="w-full">
                  Lihat Semua Artikel
                </Button>
              </CardFooter>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}
