"use client";

import React, { useState, useEffect, useTransition } from "react";
import { useRouter } from "next/navigation";
import { useForm, useFieldArray } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { toast } from "sonner";
import {
  addPurchase,
  getNextTransactionNumber,
} from "@/actions/entities/purchases";
import { Form } from "@/components/ui/form";
import { Button } from "@/components/ui/button";
import {
  PurchaseFormValues,
  Product,
  Supplier,
  EnhancedPurchaseSchema,
} from "./types";
import CombinedPurchaseForm from "./components/CombinedPurchaseForm";
import { ArrowLeft, Check, Save, Trash2 } from "lucide-react";
import { useUnsavedChangesWarning } from "@/hooks/useUnsavedChangesWarning";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialog<PERSON>itle,
} from "@/components/ui/alert-dialog";

// Props use imported types
interface EnhancedPurchasePageProps {
  products: Product[];
  suppliers: Supplier[];
}

const EnhancedPurchasePage: React.FC<EnhancedPurchasePageProps> = ({
  products,
  suppliers,
}) => {
  const router = useRouter();
  const [isPending, startTransition] = useTransition();
  const [totalAmount, setTotalAmount] = useState<number>(0);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [isFormValid, setIsFormValid] = useState(false);

  // Initialize the form with enhanced schema
  const form = useForm<PurchaseFormValues>({
    resolver: zodResolver(EnhancedPurchaseSchema),
    defaultValues: {
      items: [
        {
          productId: "",
          quantity: 1,
          costAtPurchase: 0,
          unit: "Buah",
          tax: "",
        },
      ],
      totalAmount: 0,
      invoiceRef: "",
      supplierId: "",
      paymentStatus: "paid",
      trackDelivery: false,
      notifyOnArrival: false,
      isDraft: false,
      // Add all missing fields with proper default values
      supplierEmail: "",
      transactionDate: new Date(),
      paymentDueDate: undefined,
      transactionNumber: "",
      tags: [],
      billingAddress: "",
      warehouse: "",
      memo: "",
      lampiran: [],
      deliveryDate: undefined,
    },
  });

  // Get the items field array
  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: "items",
  });

  // Watch for changes in the items array to calculate the total
  const items = form.watch("items");
  const formValues = form.watch();
  const totalAmountValue = form.watch("totalAmount");

  // Calculate total amount whenever items change
  useEffect(() => {
    const priceIncludesTax = form.watch("priceIncludesTax");

    const total = items.reduce(
      (sum: number, item: PurchaseFormValues["items"][number]) => {
        // Ensure item and properties exist before calculation
        const quantity = item?.quantity ?? 0;
        const cost = item?.costAtPurchase ?? 0;
        const taxRate = parseFloat(item?.tax || "0") / 100;

        let itemSubtotal;
        if (priceIncludesTax) {
          // If price includes tax, the total is simply quantity * cost
          itemSubtotal = quantity * cost;
        } else {
          // If price doesn't include tax, we add tax to the price
          const subtotalBeforeTax = quantity * cost;
          const taxAmount = subtotalBeforeTax * taxRate;
          itemSubtotal = subtotalBeforeTax + taxAmount;
        }

        return sum + itemSubtotal;
      },
      0
    );
    setTotalAmount(total);
    form.setValue("totalAmount", total);
  }, [items, form]);

  // Update totalAmount state when form value changes
  useEffect(() => {
    setTotalAmount(totalAmountValue);
  }, [totalAmountValue]);

  // Check form validity
  useEffect(() => {
    const isValid = form.formState.isValid;
    setIsFormValid(isValid);
  }, [form.formState.isValid]);

  // Check for unsaved changes
  useEffect(() => {
    // Consider form has unsaved changes if any meaningful data is entered
    const hasChanges = !!(
      formValues.items.some((item) => item.productId) ||
      formValues.invoiceRef ||
      formValues.supplierId ||
      formValues.supplierEmail ||
      formValues.billingAddress ||
      (formValues.tags && formValues.tags.length > 0)
    );
    setHasUnsavedChanges(hasChanges);
  }, [formValues]);

  // Use the unsaved changes warning hook
  const {
    showExitDialog,
    setShowExitDialog,
    // pendingNavigation, // Unused variable
    handleNavigation,
    confirmNavigation,
    cancelNavigation,
  } = useUnsavedChangesWarning(hasUnsavedChanges);

  // Handle product selection
  const handleProductChange = (index: number, productId: string) => {
    const selectedProduct: Product | undefined = products.find(
      (p) => p.id === productId
    );
    // Check if selectedProduct exists and its cost is a number
    if (selectedProduct) {
      // Set cost if available
      if (typeof selectedProduct.cost === "number") {
        const costValue = selectedProduct.cost; // Assign to variable to help TS inference
        form.setValue(`items.${index}.costAtPurchase`, costValue);
      }

      // Set unit from product
      if (selectedProduct.unit) {
        form.setValue(`items.${index}.unit`, selectedProduct.unit);
      }

      // Force recalculation of total immediately
      const currentItems = form.getValues("items");

      // Ensure the item exists before trying to update it
      if (currentItems[index]) {
        if (selectedProduct.cost) {
          currentItems[index].costAtPurchase = selectedProduct.cost;
        }
        if (selectedProduct.unit) {
          currentItems[index].unit = selectedProduct.unit;
        }
      }

      const priceIncludesTax = form.watch("priceIncludesTax");

      const total = currentItems.reduce(
        (sum: number, item: PurchaseFormValues["items"][number]) => {
          // Ensure item and properties exist before calculation
          const quantity = item?.quantity ?? 0;
          const cost = item?.costAtPurchase ?? 0;
          const taxRate = parseFloat(item?.tax || "0") / 100;

          let itemSubtotal;
          if (priceIncludesTax) {
            // If price includes tax, the total is simply quantity * cost
            itemSubtotal = quantity * cost;
          } else {
            // If price doesn't include tax, we add tax to the price
            const subtotalBeforeTax = quantity * cost;
            const taxAmount = subtotalBeforeTax * taxRate;
            itemSubtotal = subtotalBeforeTax + taxAmount;
          }

          return sum + itemSubtotal;
        },
        0
      );

      setTotalAmount(total);
      form.setValue("totalAmount", total);
    }
  };

  // Save as draft to database
  const saveAsDraft = () => {
    startTransition(async () => {
      try {
        // Generate auto values if needed
        let autoTransactionNumber = formValues.transactionNumber;
        let autoInvoiceRef = formValues.invoiceRef;

        // If transaction number is empty, generate one
        if (!formValues.transactionNumber) {
          autoTransactionNumber = await getNextTransactionNumber("TRX");
        }

        // If invoice reference is empty, generate one
        if (!formValues.invoiceRef) {
          autoInvoiceRef = await getNextTransactionNumber("INV");
        }

        // Set isDraft to true and include all fields
        const purchaseData = {
          items: formValues.items,
          totalAmount: formValues.totalAmount,
          invoiceRef: autoInvoiceRef,
          supplierId: formValues.supplierId,
          isDraft: true,
          // Include additional fields
          supplierEmail: formValues.supplierEmail || "",
          transactionDate: formValues.transactionDate || new Date(),
          paymentDueDate: formValues.paymentDueDate,
          transactionNumber: autoTransactionNumber,
          tags: formValues.tags || [],
          billingAddress: formValues.billingAddress || "",
          warehouse: formValues.warehouse || "",
          memo: formValues.memo || "",
          lampiran: formValues.lampiran || [],
        };

        const result = await addPurchase(purchaseData);
        if (result.success) {
          toast.success("Pembelian berhasil disimpan sebagai draft!");

          // Redirect to purchases page
          router.push("/dashboard/purchases");
        } else if (result.error) {
          toast.error(result.error);
        } else {
          toast.error("Terjadi kesalahan yang tidak diketahui.");
        }
      } catch (error) {
        console.error("Submit Error:", error);
        toast.error("Gagal menghubungi server.");
      }
    });
  };

  // Handle form submission
  const onSubmit = (values: PurchaseFormValues) => {
    startTransition(async () => {
      try {
        // Generate auto values if needed
        let autoTransactionNumber = values.transactionNumber;
        let autoInvoiceRef = values.invoiceRef;

        // If transaction number is empty, generate one
        if (!values.transactionNumber) {
          autoTransactionNumber = await getNextTransactionNumber("TRX");
        }

        // If invoice reference is empty, generate one
        if (!values.invoiceRef) {
          autoInvoiceRef = await getNextTransactionNumber("INV");
        }

        // Include all fields from the form
        const purchaseData = {
          items: values.items,
          totalAmount: values.totalAmount,
          invoiceRef: autoInvoiceRef,
          supplierId: values.supplierId,
          isDraft: false, // Not a draft when submitting normally
          // Include additional fields
          supplierEmail: values.supplierEmail || "",
          transactionDate: values.transactionDate || new Date(),
          paymentDueDate: values.paymentDueDate,
          transactionNumber: autoTransactionNumber,
          tags: values.tags || [],
          billingAddress: values.billingAddress || "",
          warehouse: values.warehouse || "",
          memo: values.memo || "",
          lampiran: values.lampiran || [],
        };

        const result = await addPurchase(purchaseData);
        if (result.success) {
          toast.success(result.success);

          form.reset(); // Reset form on success
          // Redirect after a short delay
          router.push("/dashboard/purchases");
        } else if (result.error) {
          toast.error(result.error);
        } else {
          toast.error("Terjadi kesalahan yang tidak diketahui.");
        }
      } catch (error) {
        console.error("Submit Error:", error);
        toast.error("Gagal menghubungi server.");
      }
    });
  };

  return (
    <div className="container px-4 py-6">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h1 className="text-2xl font-bold">Tambah Pembelian Baru</h1>
          <p className="text-muted-foreground">
            Catat transaksi pembelian baru dengan mengisi detail di bawah ini
          </p>
        </div>
        <Button
          variant="outline"
          className="gap-2 cursor-pointer"
          onClick={() => handleNavigation("/dashboard/purchases")}
        >
          <ArrowLeft className="h-4 w-4" />
          Kembali
        </Button>
      </div>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)}>
          <div className="space-y-6">
            {/* Main Form Content */}
            <CombinedPurchaseForm
              control={form.control}
              isPending={isPending}
              products={products}
              suppliers={suppliers}
              items={items}
              fields={fields}
              append={append}
              remove={remove}
              handleProductChange={handleProductChange}
              totalAmount={totalAmount}
            />

            {/* Purchase Summary */}
            {/* <PurchaseTransactionSummary
              formValues={formValues}
              totalAmount={totalAmount}
              isPending={isPending}
              itemCount={items.length}
              suppliers={suppliers}
            /> */}
          </div>

          {/* Form Actions */}
          <div className="mt-8 pt-6 border-t border-gray-200 dark:border-gray-700 flex justify-end">
            <div className="flex gap-4">
              <Button
                type="button"
                variant="outline"
                disabled={isPending}
                className="cursor-pointer"
                onClick={() => handleNavigation("/dashboard/purchases")}
              >
                Batal
              </Button>
              <Button
                type="button"
                variant="secondary"
                disabled={isPending || !isFormValid}
                className="gap-2 cursor-pointer"
                onClick={saveAsDraft}
              >
                <Save className="h-4 w-4" />
                <span>Simpan ke Draft</span>
              </Button>
              <Button
                type="submit"
                disabled={isPending}
                className="gap-2 cursor-pointer"
              >
                {isPending ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                    <span>Menyimpan...</span>
                  </>
                ) : (
                  <>
                    <Check className="h-4 w-4" />
                    <span>Simpan Pembelian</span>
                  </>
                )}
              </Button>
            </div>
          </div>
        </form>
      </Form>

      {/* Unsaved Changes Dialog */}
      <AlertDialog open={showExitDialog} onOpenChange={setShowExitDialog}>
        <AlertDialogContent className="w-[95%] max-w-md md:max-w-xl lg:max-w-2xl">
          <AlertDialogHeader>
            <AlertDialogTitle>Perubahan Belum Tersimpan</AlertDialogTitle>
            <AlertDialogDescription>
              Anda memiliki perubahan yang belum tersimpan. Jika Anda
              meninggalkan halaman ini, perubahan Anda akan hilang.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter className="flex flex-col space-y-2 md:flex-row md:space-y-0 md:space-x-2 md:justify-end">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-2 w-full">
              <AlertDialogCancel
                onClick={cancelNavigation}
                className="cursor-pointer w-full"
              >
                Kembali ke Form
              </AlertDialogCancel>
              <AlertDialogAction
                onClick={confirmNavigation}
                className="cursor-pointer bg-red-500 hover:bg-red-600 w-full"
              >
                Buang Perubahan
              </AlertDialogAction>
              <Button
                type="button"
                variant="default"
                className="cursor-pointer w-full"
                onClick={() => {
                  saveAsDraft();
                  setShowExitDialog(false);
                }}
              >
                <Save className="h-4 w-4 mr-2" />
                Simpan ke Draft
              </Button>
            </div>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
};

export default EnhancedPurchasePage;
