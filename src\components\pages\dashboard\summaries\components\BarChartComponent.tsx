"use client";

import React from "react";
import {
  <PERSON><PERSON>hart,
  Bar,
  XAxis,
  <PERSON>A<PERSON>s,
  CartesianGrid,
  <PERSON>lt<PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>ons<PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
} from "recharts";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";

// Chart color palette
export const CHART_COLORS = [
  "#8884d8", // Purple
  "#82ca9d", // Green
  "#ffc658", // Yellow
  "#ff8042", // Orange
  "#0088fe", // Blue
];

// Helper function to format currency
export function formatCurrency(value: number) {
  return new Intl.NumberFormat("id-ID", {
    style: "currency",
    currency: "IDR",
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(value);
}

export interface ChartDataPoint {
  name: string;
  [key: string]: string | number;
}

interface BarChartComponentProps {
  data: ChartDataPoint[];
  title?: string;
  description?: string;
  dataKeys: string[];
  colors?: string[];
  height?: number | string;
  valueFormatter?: (value: number) => string;
  className?: string;
  showLegend?: boolean;
}

export function BarChartComponent({
  data,
  title,
  description,
  dataKeys,
  colors = CHART_COLORS,
  height = 300,
  valueFormatter = formatCurrency,
  className = "",
  showLegend = true,
}: BarChartComponentProps) {
  return (
    <Card className={`border-none shadow-md dark:bg-gray-800 ${className}`}>
      {(title || description) && (
        <CardHeader>
          {title && <CardTitle>{title}</CardTitle>}
          {description && <CardDescription>{description}</CardDescription>}
        </CardHeader>
      )}
      <CardContent>
        <div style={{ height: typeof height === "number" ? `${height}px` : height }}>
          <ResponsiveContainer width="100%" height="100%">
            <BarChart
              data={data}
              margin={{
                top: 20,
                right: 30,
                left: 20,
                bottom: 5,
              }}
            >
              <CartesianGrid strokeDasharray="3 3" opacity={0.3} />
              <XAxis dataKey="name" />
              <YAxis
                tickFormatter={(value) => 
                  typeof value === 'number' ? valueFormatter(value) : value
                }
                width={100}
              />
              <RechartsTooltip
                formatter={(value: number, name: string) => [
                  typeof value === 'number' ? valueFormatter(value) : value,
                  name,
                ]}
              />
              {showLegend && <Legend />}
              {dataKeys.map((key, index) => (
                <Bar
                  key={key}
                  dataKey={key}
                  fill={colors[index % colors.length]}
                  name={key}
                />
              ))}
            </BarChart>
          </ResponsiveContainer>
        </div>
      </CardContent>
    </Card>
  );
}
