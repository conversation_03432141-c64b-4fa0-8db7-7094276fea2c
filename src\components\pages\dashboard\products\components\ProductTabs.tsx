"use client";

import React from "react";
import { Box, Info, Layers } from "lucide-react";
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import ProductDetailsTab from "./ProductDetailsTab";
import ProductInventoryTab from "./ProductInventoryTab";

interface Category {
  id: string;
  name: string;
}

interface ProductTabsProps {
  id: string; // Add product ID
  name: string;
  sku: string | null;
  description: string | null;
  barcode?: string;
  taxRate?: number;
  tags?: string[];
  stock: number;
  minStockLevel?: number;
  trackInventory?: boolean;
  hasVariants?: boolean;
  weight?: number | null;
  dimensions?: {
    length?: number;
    width?: number;
    height?: number;
  };
}

const ProductTabs: React.FC<ProductTabsProps> = ({
  id,
  name,
  sku,
  description,
  barcode,
  taxRate,
  tags,
  stock,
  minStockLevel,
  trackInventory,
  hasVariants,
  weight,
  dimensions,
}) => {
  return (
    <Tabs defaultValue="details" className="w-full">
      <TabsList className="grid grid-cols-3 mb-4">
        <TabsTrigger
          value="details"
          className="flex items-center gap-2 cursor-pointer"
        >
          <Info className="h-4 w-4" />
          <span>Detail</span>
        </TabsTrigger>
        <TabsTrigger
          value="inventory"
          className="flex items-center gap-2 cursor-pointer"
        >
          <Layers className="h-4 w-4" />
          <span>Inventaris</span>
        </TabsTrigger>
        <TabsTrigger
          value="shipping"
          className="flex items-center gap-2 cursor-pointer"
        >
          <Box className="h-4 w-4" />
          <span>Pengiriman</span>
        </TabsTrigger>
      </TabsList>

      {/* Details Tab */}
      <TabsContent value="details">
        <ProductDetailsTab
          id={id}
          name={name}
          sku={sku}
          barcode={barcode}
          taxRate={taxRate}
          description={description}
          tags={tags}
        />
      </TabsContent>

      {/* Inventory Tab */}
      <TabsContent value="inventory">
        <ProductInventoryTab
          stock={stock}
          minStockLevel={minStockLevel}
          trackInventory={trackInventory}
          hasVariants={hasVariants}
        />
      </TabsContent>
    </Tabs>
  );
};

export default ProductTabs;
