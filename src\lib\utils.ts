import { clsx, type ClassValue } from "clsx";
import { twMerge } from "tailwind-merge";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

/**
 * Format a date string to a localized format
 * @param dateString ISO date string
 * @returns Formatted date string
 */
export function formatDate(dateString: string): string {
  if (!dateString) return "-";

  try {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat("id-ID", {
      day: "2-digit",
      month: "short",
      year: "numeric",
    }).format(date);
  } catch (error) {
    console.error("Error formatting date:", error);
    return dateString;
  }
}

/**
 * Format a number as Indonesian currency with dot separators
 * @param value Number to format
 * @param withSymbol Whether to include the 'Rp' symbol
 * @returns Formatted currency string
 */
export function formatCurrency(
  value: number,
  withSymbol: boolean = true
): string {
  if (isNaN(value)) return withSymbol ? "Rp 0" : "0";

  // Format with dots as thousand separators
  const formatted = value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ".");

  return withSymbol ? `Rp ${formatted}` : formatted;
}
