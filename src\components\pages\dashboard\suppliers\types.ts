export interface Supplier {
  id: string;

  // Basic Info
  name: string;
  firstName: string | null;
  middleName: string | null;
  lastName: string | null;
  contactName: string | null;

  // Contact Information
  phone: string | null;
  telephone: string | null;
  fax: string | null;
  email: string | null;

  // Identity Information
  identityType: string | null;
  identityNumber: string | null;
  NIK: string | null;
  NPWP: string | null;

  // Company Information
  companyName: string | null;
  otherInfo: string | null;

  // Address Information
  address: string | null;
  billingAddress: string | null;
  shippingAddress: string | null;
  sameAsShipping: boolean;

  // Bank Information
  bankName: string | null;
  bankBranch: string | null;
  accountHolder: string | null;
  accountNumber: string | null;

  // Additional fields
  notes: string | null;

  // Timestamps
  createdAt: Date;
  updatedAt: Date;
}

export interface ColumnVisibility {
  name: boolean;
  contactName: boolean;
  email: boolean;
  phone: boolean;
  address: boolean;
  createdAt: boolean;
  updatedAt: boolean;
  notes: boolean;
}

export const supplierCategories = [
  { value: "product", label: "Produk" },
  { value: "service", label: "Jasa" },
  { value: "both", label: "Produk & Jasa" },
];

export interface SupplierStatus {
  active: number;
  inactive: number;
  total: number;
}
