"use client";

import React from "react";
import { Control } from "react-hook-form";
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { ProductFormValues } from "../types";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardFooter,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  CreditCard,
  Tag,
  Percent,
  Package,
  AlertCircle,
  TrendingUp,
  TrendingDown,
} from "lucide-react";
import { Separator } from "@/components/ui/separator";

// Helper function to allow only numbers and decimal point in input
const handleNumberInput = (e: React.KeyboardEvent<HTMLInputElement>) => {
  // Allow: backspace, delete, tab, escape, enter, decimal point, and numbers
  if (
    e.key === "Backspace" ||
    e.key === "Delete" ||
    e.key === "Tab" ||
    e.key === "Escape" ||
    e.key === "Enter" ||
    e.key === "." ||
    e.key === "," ||
    (e.key >= "0" && e.key <= "9")
  ) {
    // Allow comma as decimal separator but convert to period for value
    if (e.key === ",") {
      e.preventDefault();
      const input = e.target as HTMLInputElement;
      const start = input.selectionStart || 0;
      const end = input.selectionEnd || 0;
      const value = input.value;
      input.value = value.substring(0, start) + "." + value.substring(end);
      input.selectionStart = input.selectionEnd = start + 1;
    }
    // Check if decimal point already exists when trying to add another
    if (e.key === "." && (e.target as HTMLInputElement).value.includes(".")) {
      e.preventDefault();
    }
    return;
  }
  // Block all other keys
  e.preventDefault();
};

// Format number with Indonesian format (using dots as thousand separators)
const formatToIDR = (value: string | number): string => {
  if (!value && value !== 0) return "";
  if (value === 0 || value === "0") return "";

  // Convert to string and remove any non-numeric characters except decimal point
  const numStr = String(value).replace(/[^\d.]/g, "");

  // Split by decimal point
  const parts = numStr.split(".");

  // Format the integer part with dots as thousand separators
  parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ".");

  // Return formatted number (with decimal part if exists)
  return parts.join(",");
};

// Parse formatted number back to raw value
const parseFromIDR = (formattedValue: string): string => {
  // Remove all dots and replace comma with dot for decimal
  return formattedValue.replace(/\./g, "").replace(",", ".");
};

interface ProductPricingInventoryProps {
  control: Control<ProductFormValues>;
  isPending: boolean;
}

const ProductPricingInventory: React.FC<ProductPricingInventoryProps> = ({
  control,
  isPending,
}) => {
  // Calculate profit if both price and cost are set
  const price = parseFloat(control._formValues.price as string) || 0;
  const discountPrice =
    parseFloat(control._formValues.discountPrice as string) || undefined;
  const cost = parseFloat(control._formValues.cost as string) || 0;

  // Use discount price for calculations if available
  const effectivePrice = discountPrice !== undefined ? discountPrice : price;

  // Calculate profit
  const profitAmount =
    effectivePrice > 0 && cost > 0 ? effectivePrice - cost : 0;
  const profitPercentage = cost > 0 ? (profitAmount / cost) * 100 : 0;

  // Determine profit status
  const isProfitable = profitAmount > 0;
  const profitStatus = isProfitable
    ? "positive"
    : profitAmount < 0
      ? "negative"
      : "neutral";

  return (
    <div className="space-y-8">
      {/* Pricing Section */}
      <Card className="overflow-hidden border-2 border-gray-100 dark:border-gray-800 pt-0">
        <CardHeader className="bg-gray-50 dark:bg-gray-800 pb-3 pt-4">
          <CardTitle className="text-lg flex items-center gap-2">
            <Tag className="h-5 w-5 text-primary" />
            Informasi Harga
          </CardTitle>
        </CardHeader>
        <CardContent className="pt-0">
          <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
            {/* Harga Jual */}
            <FormField
              control={control}
              name="price"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="flex items-center gap-1.5">
                    <CreditCard className="h-4 w-4 text-primary" />
                    Harga Jual <span className="text-red-500 font-bold">*</span>
                  </FormLabel>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                      <span className="text-gray-500 sm:text-sm">Rp</span>
                    </div>
                    <FormControl className="form-control">
                      <Input
                        type="text"
                        className="pl-10 font-medium"
                        placeholder="0"
                        name="price"
                        value={formatToIDR(field.value)}
                        onFocus={() => {
                          if (String(field.value) === "0") {
                            field.onChange("");
                          }
                        }}
                        onBlur={() => {
                          if (!field.value && field.value !== 0) {
                            field.onChange("0");
                          }
                        }}
                        onKeyDown={handleNumberInput}
                        onChange={(e) => {
                          // Get the raw value by removing formatting
                          const rawValue = parseFromIDR(e.target.value);

                          // Sanitize the input to ensure it's a valid number
                          const parts = rawValue.split(".");
                          const sanitized =
                            parts[0] +
                            (parts.length > 1
                              ? "." + parts.slice(1).join("")
                              : "");

                          // Update the form with the raw value
                          field.onChange(sanitized || "0");
                        }}
                        disabled={isPending}
                      />
                    </FormControl>
                  </div>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Harga Diskon */}
            <FormField
              control={control}
              name="discountPrice"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="flex items-center gap-1.5">
                    <TrendingDown className="h-4 w-4 text-green-500" />
                    Harga Diskon{" "}
                    <span className="text-xs text-gray-500">(Opsional)</span>
                  </FormLabel>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                      <span className="text-gray-500 sm:text-sm">Rp</span>
                    </div>
                    <FormControl>
                      <Input
                        type="text"
                        className="pl-10 font-medium"
                        placeholder="0"
                        name="discountPrice"
                        value={
                          field.value === undefined
                            ? ""
                            : formatToIDR(field.value)
                        }
                        onFocus={() => {
                          if (
                            field.value === undefined ||
                            String(field.value) === "0"
                          ) {
                            field.onChange("");
                          }
                        }}
                        onBlur={() => {
                          const stringValue = String(field.value);
                          if (
                            !field.value ||
                            stringValue === "0" ||
                            stringValue === ""
                          ) {
                            field.onChange(undefined);
                          }
                        }}
                        onKeyDown={handleNumberInput}
                        onChange={(e) => {
                          const rawValue = parseFromIDR(e.target.value);

                          if (!rawValue) {
                            field.onChange(undefined);
                            return;
                          }

                          const parts = rawValue.split(".");
                          const sanitized =
                            parts[0] +
                            (parts.length > 1
                              ? "." + parts.slice(1).join("")
                              : "");
                          field.onChange(sanitized);
                        }}
                        disabled={isPending}
                      />
                    </FormControl>
                  </div>
                </FormItem>
              )}
            />

            {/* Harga Beli */}
            <FormField
              control={control}
              name="cost"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="flex items-center gap-1.5">
                    <TrendingUp className="h-4 w-4 text-blue-500" />
                    Harga Beli{" "}
                    <span className="text-xs text-gray-500">(Opsional)</span>
                  </FormLabel>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                      <span className="text-gray-500 sm:text-sm">Rp</span>
                    </div>
                    <FormControl>
                      <Input
                        type="text"
                        className="pl-10 font-medium"
                        placeholder="0"
                        name="cost"
                        value={formatToIDR(field.value)}
                        onFocus={() => {
                          if (String(field.value) === "0") {
                            field.onChange("");
                          }
                        }}
                        onBlur={() => {
                          if (!field.value && field.value !== 0) {
                            field.onChange("0");
                          }
                        }}
                        onKeyDown={handleNumberInput}
                        onChange={(e) => {
                          // Get the raw value by removing formatting
                          const rawValue = parseFromIDR(e.target.value);

                          // Sanitize the input to ensure it's a valid number
                          const parts = rawValue.split(".");
                          const sanitized =
                            parts[0] +
                            (parts.length > 1
                              ? "." + parts.slice(1).join("")
                              : "");

                          // Update the form with the raw value
                          field.onChange(sanitized || "0");
                        }}
                        disabled={isPending}
                      />
                    </FormControl>
                  </div>
                </FormItem>
              )}
            />

            {/* Tax Rate */}
            <FormField
              control={control}
              name="taxRate"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="flex items-center gap-1.5">
                    <Percent className="h-4 w-4 text-orange-500" />
                    Tarif Pajak{" "}
                    <span className="text-xs text-gray-500">(Opsional)</span>
                  </FormLabel>
                  <div className="relative">
                    <FormControl>
                      <Input
                        type="text"
                        className="pr-8 font-medium"
                        placeholder="0"
                        value={
                          String(field.value) === "0" ? "" : String(field.value)
                        }
                        onFocus={() => {
                          if (String(field.value) === "0") {
                            field.onChange("");
                          }
                        }}
                        onBlur={() => {
                          if (!field.value && field.value !== 0) {
                            field.onChange("0");
                          }
                        }}
                        onKeyDown={handleNumberInput}
                        onChange={(e) => {
                          let value = e.target.value.replace(/[^0-9.]/g, "");
                          const parts = value.split(".");
                          value =
                            parts[0] +
                            (parts.length > 1
                              ? "." + parts.slice(1).join("")
                              : "");
                          const numValue = parseFloat(value);
                          if (!isNaN(numValue) && numValue > 100) {
                            value = "100";
                          }
                          field.onChange(value || "0");
                        }}
                        disabled={isPending}
                      />
                    </FormControl>
                    <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                      <span className="text-gray-500 sm:text-sm">%</span>
                    </div>
                  </div>
                </FormItem>
              )}
            />
          </div>
        </CardContent>

        {/* Profit Calculation */}
        {cost > 0 && price > 0 && (
          <CardFooter className="bg-gray-50 dark:bg-gray-800 border-t border-gray-100 dark:border-gray-700 py-3">
            <div className="w-full flex justify-between items-center">
              <span className="text-sm font-medium">Keuntungan:</span>
              <div className="flex items-center gap-2">
                <span className="font-medium">
                  Rp {formatToIDR(profitAmount)}
                </span>
                {profitAmount !== 0 && (
                  <Badge
                    variant="outline"
                    className={`${
                      profitStatus === "positive"
                        ? "bg-green-50 text-green-700 dark:bg-green-900/20 dark:text-green-400"
                        : profitStatus === "negative"
                          ? "bg-red-50 text-red-700 dark:bg-red-900/20 dark:text-red-400"
                          : ""
                    }`}
                  >
                    {profitStatus === "positive" ? "+" : ""}
                    {profitPercentage.toFixed(1)}%
                  </Badge>
                )}
              </div>
            </div>
          </CardFooter>
        )}
      </Card>
    </div>
  );
};

export default ProductPricingInventory;
