import { auth } from "@/lib/auth";
import { db } from "@/lib/prisma";
import React from "react";
import ProfileSettings from "@/components/pages/dashboard/settings/profile/profile-settings";
import DashboardLayout from "@/components/layout/dashboardlayout";
import SettingsLayout from "@/components/pages/dashboard/settings/settings-layout";

const ProfileSettingsPage = async () => {
  const session = await auth();

  // Fetch user data from database - we can assume session exists due to middleware protection
  const user = session?.user?.id
    ? await db.user.findUnique({
        where: {
          id: session.user.id,
        },
        select: {
          id: true,
          name: true,
          email: true,
          username: true,
          image: true,
          phone: true,
          bio: true,
          birthday: true,
          role: true,
          lastLogin: true,
          createdAt: true,
          currentPlan: true,
          subscriptionExpiry: true,
          companyId: true, // Add company ID field
        },
      })
    : null;

  // Use a default user object with empty values if user is null
  const userData = user || {
    id: session?.user?.id || "",
    name: session?.user?.name || "",
    email: session?.user?.email || "",
    username: "",
    image: session?.user?.image || "",
    phone: null,
    bio: null,
    birthday: null,
    role: session?.user?.role || "OWNER",
    lastLogin: null,
    createdAt: new Date(),
    currentPlan: "FREE",
    subscriptionExpiry: null,
    companyId: null, // Add default company ID field
  };

  return (
    <DashboardLayout>
      <SettingsLayout>
        <ProfileSettings user={userData} />
      </SettingsLayout>
    </DashboardLayout>
  );
};

export default ProfileSettingsPage;
