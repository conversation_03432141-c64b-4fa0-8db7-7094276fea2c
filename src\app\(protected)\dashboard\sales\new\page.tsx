import React from "react";
import DashboardLayout from "@/components/layout/dashboardlayout";
import EnhancedSalePage from "@/components/pages/dashboard/sales/new/enhanced-index";
import { getProducts } from "@/lib/get-products";
import { Product } from "@/components/pages/dashboard/sales/new/types";
import { ErrorCard } from "@/components/ui/error-card";
import { Metadata } from "next";

// Metadata for the page
export const metadata: Metadata = {
  title: "Tambah Penjualan - Kasir Online",
  description: "Tambah transaksi penjualan baru",
};

// This is an async Server Component
export default async function NewSale() {
  try {
    // Fetch products using our utility function that handles employee access
    const serializedProducts = await getProducts({
      includeOutOfStock: false, // Only show products with stock > 0
      orderBy: "createdAt", // Order by creation date to get newest products
      orderDirection: "desc", // Newest first
      excludeDrafts: true, // Exclude draft products
    });

    // Filter out products with no stock (additional check)
    const availableProducts = serializedProducts.filter(
      (product) => (product.stock || 0) > 0
    );

    // If no products were found, it could be due to authentication issues
    if (availableProducts.length === 0) {
      return (
        <DashboardLayout>
          <ErrorCard
            title="Tidak ada produk"
            description="Tidak ada produk yang tersedia untuk dijual. Tambahkan produk terlebih dahulu."
            retryLink="/dashboard/products/new"
            retryLabel="Tambah Produk"
          />
        </DashboardLayout>
      );
    }

    // Adapt the products to match the expected Product interface
    const adaptedProducts: Product[] = availableProducts.map((product) => ({
      id: product.id,
      name: product.name,
      price: product.price,
      stock: product.stock || 0,
      image: product.image || undefined,
      createdAt: product.createdAt || undefined,
    }));

    return (
      <DashboardLayout>
        <EnhancedSalePage products={adaptedProducts} />
      </DashboardLayout>
    );
  } catch (error) {
    console.error("Error loading products for sale page:", error);
    return (
      <DashboardLayout>
        <ErrorCard
          title="Error"
          description="Terjadi kesalahan saat memuat data produk. Silakan coba lagi nanti."
          retryLink="/dashboard/sales/new"
          retryLabel="Coba Lagi"
        />
      </DashboardLayout>
    );
  }
}
