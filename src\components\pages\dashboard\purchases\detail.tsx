"use client";

import type { NextPage } from "next";
import Head from "next/head";
import Link from "next/link";
import { useRouter } from "next/navigation";
import React from "react";
import { ArrowLeftIcon, PencilIcon } from "@heroicons/react/24/outline";
import {
  Trash,
  FileText,
  Tag,
  Paperclip,
  File,
  Image as ImageIcon,
  Printer,
} from "lucide-react";
import { toast } from "sonner";

import DashboardLayout from "@/components/layout/dashboardlayout";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { deletePurchase } from "@/actions/entities/purchases";

// Import types from the types.ts file
import type { Purchase } from "./types";

interface PurchaseDetailPageProps {
  purchase: Purchase;
}

const PurchaseDetailPage: NextPage<PurchaseDetailPageProps> = ({
  purchase,
}) => {
  const router = useRouter();
  const purchaseDate = new Date(purchase.purchaseDate);
  const [isDeleting, setIsDeleting] = React.useState(false);
  const [isPrintDialogOpen, setIsPrintDialogOpen] = React.useState(false);
  const [selectedTemplate, setSelectedTemplate] =
    React.useState<string>("horizontal1");
  const [selectedOrientation, setSelectedOrientation] = React.useState<
    "horizontal" | "vertical"
  >("horizontal");

  // Handle delete purchase
  const handleDeletePurchase = async () => {
    setIsDeleting(true);
    try {
      const result = await deletePurchase(purchase.id);
      if (result.success) {
        toast.success(result.success);
        router.push("/dashboard/purchases");
      } else if (result.error) {
        toast.error(result.error);
      }
    } catch (error) {
      console.error("Error deleting purchase:", error);
      toast.error("Terjadi kesalahan saat menghapus pembelian.");
    } finally {
      setIsDeleting(false);
    }
  };

  // Handle print invoice
  const handlePrint = () => {
    // Close the dialog
    setIsPrintDialogOpen(false);

    // Create a new window for printing
    const printWindow = window.open("", "_blank");
    if (!printWindow) {
      toast.error(
        "Popup diblokir oleh browser. Mohon izinkan popup untuk mencetak faktur."
      );
      return;
    }

    // Import the template dynamically to avoid bundling all templates
    import("./templates")
      .then(({ getInvoiceTemplate }) => {
        // Use the selected template directly
        const templateContent = getInvoiceTemplate(
          selectedTemplate as any,
          purchase
        );

        // Write the template content to the new window using a more modern approach
        const parser = new DOMParser();
        const htmlDoc = parser.parseFromString(templateContent, "text/html");

        // Clear the document and append the new content
        printWindow.document.documentElement.innerHTML = "";
        Array.from(htmlDoc.head.childNodes).forEach((node) => {
          printWindow.document.head.appendChild(
            printWindow.document.importNode(node, true)
          );
        });
        Array.from(htmlDoc.body.childNodes).forEach((node) => {
          printWindow.document.body.appendChild(
            printWindow.document.importNode(node, true)
          );
        });

        // Wait for images and resources to load before printing
        printWindow.onload = () => {
          printWindow.print();
          // printWindow.close(); // Uncomment to auto-close after print dialog
        };
      })
      .catch((error) => {
        console.error("Error loading templates:", error);
        toast.error("Terjadi kesalahan saat memuat template faktur.");
        printWindow.close();
      });
  };

  return (
    <DashboardLayout>
      <Head>
        <title>Detail Pembelian - Kasir Online</title>
      </Head>

      <div className="w-full px-4 py-6">
        {/* Back button and Edit button */}
        <div className="flex justify-between items-center mb-6">
          <Button
            variant="outline"
            size="sm"
            className="flex items-center gap-2"
            onClick={() => router.back()}
          >
            <ArrowLeftIcon className="h-4 w-4" />
            Kembali
          </Button>

          <Button
            asChild
            variant="default"
            size="sm"
            className="flex items-center gap-2"
          >
            <Link
              href={`/dashboard/purchases/edit/${purchase.transactionNumber || purchase.id}`}
            >
              <PencilIcon className="h-4 w-4" />
              Edit Pembelian
            </Link>
          </Button>
        </div>

        <Card>
          <CardHeader>
            <div className="flex justify-between items-start">
              <div>
                <CardTitle className="text-2xl font-bold tracking-tight">
                  Pembelian #
                  {purchase.transactionNumber || purchase.id.substring(0, 8)}
                </CardTitle>
                <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                  {purchaseDate.toLocaleDateString("id-ID", {
                    day: "numeric",
                    month: "long",
                    year: "numeric",
                    hour: "2-digit",
                    minute: "2-digit",
                  })}
                </p>
                {purchase.invoiceRef && (
                  <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                    No. Invoice: {purchase.invoiceRef}
                  </p>
                )}
              </div>
              <Badge
                variant="outline"
                className="bg-green-50 text-green-700 dark:bg-green-900/20 dark:text-green-400"
              >
                Selesai
              </Badge>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              {/* Purchase Items */}
              <div>
                <h3 className="text-lg font-medium mb-3">Item Pembelian</h3>
                <div className="overflow-x-auto border border-gray-200 dark:border-gray-700 rounded-lg">
                  <table className="w-full text-sm text-left text-gray-500 dark:text-gray-400">
                    <thead className="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-800 dark:text-gray-300">
                      <tr>
                        <th scope="col" className="px-6 py-3">
                          Produk
                        </th>
                        <th scope="col" className="px-6 py-3 text-right">
                          Harga Beli
                        </th>
                        <th scope="col" className="px-6 py-3 text-right">
                          Jumlah
                        </th>
                        <th scope="col" className="px-6 py-3 text-right">
                          Subtotal
                        </th>
                      </tr>
                    </thead>
                    <tbody>
                      {purchase.items.map((item) => (
                        <tr
                          key={item.id}
                          className="bg-white border-b dark:bg-gray-900 dark:border-gray-700"
                        >
                          <td className="px-6 py-4 font-medium text-gray-900 dark:text-gray-100">
                            <Link
                              href={`/dashboard/products/detail/${item.productId}`}
                              className="hover:text-blue-600 text-blue-500 dark:hover:text-blue-400 cursor-pointer underline"
                            >
                              {item.product.name}
                            </Link>
                          </td>
                          <td className="px-6 py-4 text-right">
                            Rp {item.costAtPurchase.toLocaleString("id-ID")}
                          </td>
                          <td className="px-6 py-4 text-right">
                            {item.quantity}
                          </td>
                          <td className="px-6 py-4 text-right font-medium">
                            Rp{" "}
                            {(
                              item.quantity * item.costAtPurchase
                            ).toLocaleString("id-ID")}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                    <tfoot>
                      <tr className="bg-gray-50 dark:bg-gray-800">
                        <td
                          colSpan={3}
                          className="px-6 py-4 text-right font-medium text-gray-700 dark:text-gray-300"
                        >
                          Total
                        </td>
                        <td className="px-6 py-4 text-right font-bold text-gray-900 dark:text-gray-100">
                          Rp {purchase.totalAmount.toLocaleString("id-ID")}
                        </td>
                      </tr>
                    </tfoot>
                  </table>
                </div>
              </div>

              <Separator />

              {/* Purchase Details */}
              <div>
                <h3 className="text-lg font-medium mb-3">
                  Informasi Pembelian
                </h3>

                {/* Main Purchase Information - table-like format */}
                <div className="grid md:grid-cols-2 gap-6 text-sm mb-6">
                  {/* Left Column */}
                  <div className="space-y-2">
                    <div className="grid grid-cols-2 gap-x-4 gap-y-2">
                      <div className="font-medium">No. Transaksi</div>
                      <div>
                        {purchase.transactionNumber ||
                          purchase.id.substring(0, 8)}
                      </div>

                      <div className="font-medium">Waktu</div>
                      <div>
                        {purchaseDate.toLocaleTimeString("id-ID", {
                          hour: "2-digit",
                          minute: "2-digit",
                        })}
                      </div>

                      <div className="font-medium">Supplier</div>
                      <div>
                        {purchase.supplier ? (
                          <Link
                            href={`/dashboard/suppliers/${purchase.supplierId}`}
                            className="hover:text-blue-600 text-blue-500 dark:hover:text-blue-400 cursor-pointer underline"
                          >
                            {purchase.supplier.name}
                          </Link>
                        ) : (
                          "-"
                        )}
                      </div>

                      <div className="font-medium">Jumlah Item</div>
                      <div>{purchase.items.length} item</div>

                      <div className="font-medium">Status</div>
                      <div>
                        <Badge
                          variant="outline"
                          className="bg-green-50 text-green-700 dark:bg-green-900/20 dark:text-green-400"
                        >
                          Selesai
                        </Badge>
                      </div>

                      <div className="font-medium">Alamat Penagihan</div>
                      <div>{purchase.billingAddress || "-"}</div>
                    </div>
                  </div>

                  {/* Right Column */}
                  <div className="space-y-2">
                    <div className="grid grid-cols-2 gap-x-4 gap-y-2">
                      <div className="font-medium">Tanggal Pembelian</div>
                      <div>
                        {purchaseDate.toLocaleDateString("id-ID", {
                          day: "numeric",
                          month: "long",
                          year: "numeric",
                        })}
                      </div>

                      <div className="font-medium">Email Supplier</div>
                      <div>{purchase.supplierEmail || "-"}</div>

                      <div className="font-medium">No. Invoice</div>
                      <div>{purchase.invoiceRef || "-"}</div>

                      <div className="font-medium">Tgl. Jatuh Tempo</div>
                      <div>
                        {purchase.paymentDueDate
                          ? new Date(
                              purchase.paymentDueDate
                            ).toLocaleDateString("id-ID", {
                              day: "numeric",
                              month: "long",
                              year: "numeric",
                            })
                          : "-"}
                      </div>

                      <div className="font-medium">Total</div>
                      <div className="font-bold">
                        Rp {purchase.totalAmount.toLocaleString("id-ID")}
                      </div>
                    </div>
                  </div>
                </div>

                {/* Divider */}
                <Separator className="my-4" />

                {/* Additional Information Section */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {/* Left Column - Memo and Lampiran */}
                  <div className="space-y-4">
                    {/* Memo */}
                    <div>
                      <div className="font-medium mb-1 flex items-center gap-1.5">
                        <FileText className="h-4 w-4 text-purple-600" />
                        Memo
                      </div>
                      <div className="p-2 bg-gray-50 dark:bg-gray-800 rounded-md min-h-[40px]">
                        {purchase.memo || "-"}
                      </div>
                    </div>

                    {/* Lampiran */}
                    <div>
                      <div className="font-medium mb-1 flex items-center gap-1.5">
                        <Paperclip className="h-4 w-4 text-amber-600" />
                        Lampiran
                      </div>
                      <div className="bg-gray-50 dark:bg-gray-800 rounded-md p-2 min-h-[40px]">
                        {purchase.lampiran && purchase.lampiran.length > 0 ? (
                          <ul className="space-y-1">
                            {purchase.lampiran.map((file, index) => {
                              const fileData =
                                typeof file === "string"
                                  ? JSON.parse(file)
                                  : file;
                              const fileName = fileData.filename;
                              const fileUrl = fileData.url;

                              // Determine file type for icon
                              const fileExt = fileName
                                .split(".")
                                .pop()
                                ?.toLowerCase();
                              let FileIcon = File;

                              if (
                                ["jpg", "jpeg", "png", "gif", "webp"].includes(
                                  fileExt || ""
                                )
                              ) {
                                FileIcon = ImageIcon;
                              } else if (fileExt === "pdf") {
                                FileIcon = FileText;
                              }

                              return (
                                <div
                                  key={index}
                                  className="flex items-center gap-2 text-sm"
                                >
                                  <FileIcon className="h-3.5 w-3.5 text-amber-600" />
                                  <a
                                    href={fileUrl}
                                    target="_blank"
                                    rel="noopener noreferrer"
                                    className="text-blue-600 hover:underline flex-1 truncate"
                                  >
                                    {fileName}
                                  </a>
                                </div>
                              );
                            })}
                          </ul>
                        ) : (
                          <div className="flex h-[30px] text-gray-500">-</div>
                        )}
                      </div>
                    </div>
                  </div>

                  {/* Right Column - Tags */}
                  <div>
                    <div className="font-medium mb-1 flex items-center gap-1.5">
                      <Tag className="h-4 w-4 text-teal-600" />
                      Tag
                    </div>
                    <div className="flex flex-wrap gap-2 p-2 min-h-[40px]">
                      {purchase.tags && purchase.tags.length > 0 ? (
                        purchase.tags.map((tag, index) => (
                          <Badge
                            key={index}
                            variant="secondary"
                            className="bg-teal-100 text-teal-800 dark:bg-teal-900/30 dark:text-teal-300 shadow-sm"
                          >
                            {tag}
                          </Badge>
                        ))
                      ) : (
                        <span className="text-gray-500">-</span>
                      )}
                    </div>
                  </div>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="pt-4 flex flex-wrap gap-4 justify-end">
                <Dialog
                  open={isPrintDialogOpen}
                  onOpenChange={setIsPrintDialogOpen}
                >
                  <DialogTrigger asChild>
                    <Button variant="outline" className="gap-2">
                      <Printer className="h-4 w-4" />
                      Cetak Faktur
                    </Button>
                  </DialogTrigger>
                  <DialogContent className="max-h-[80vh] h-[80vh] overflow-y-auto max-w-[90vw] w-[900px] overflow-x-hidden">
                    <DialogHeader>
                      <DialogTitle>Cetak Faktur Pembelian</DialogTitle>
                      <DialogDescription>
                        Pilih template faktur yang ingin Anda cetak.
                      </DialogDescription>
                    </DialogHeader>

                    <div className="grid grid-cols-1 md:grid-cols-[300px_minmax(0,1fr)] gap-6 py-4">
                      {/* Left Column - Options */}
                      <div className="space-y-6 flex flex-col h-full">
                        {/* Orientation Options */}
                        <div className="space-y-3">
                          <div className="font-medium">Jenis Faktur</div>
                          <div className="grid grid-cols-2 gap-3">
                            <div
                              className={`border rounded-md p-3 cursor-pointer ${
                                selectedOrientation === "horizontal"
                                  ? "border-blue-500 bg-blue-50 dark:bg-blue-900/20"
                                  : "hover:bg-gray-50 dark:hover:bg-gray-800"
                              }`}
                              onClick={() => {
                                setSelectedOrientation("horizontal");
                                // Update template to match orientation
                                if (selectedTemplate.startsWith("vertical")) {
                                  const styleNumber = selectedTemplate.replace(
                                    "vertical",
                                    ""
                                  );
                                  setSelectedTemplate(
                                    `horizontal${styleNumber}`
                                  );
                                }
                              }}
                            >
                              <div className="text-center">
                                <div className="flex items-center justify-center mb-2">
                                  <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    width="20"
                                    height="20"
                                    viewBox="0 0 24 24"
                                    fill="none"
                                    stroke="currentColor"
                                    strokeWidth="2"
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    className="mr-1.5 text-blue-600"
                                  >
                                    <rect
                                      x="3"
                                      y="3"
                                      width="18"
                                      height="18"
                                      rx="2"
                                      ry="2"
                                    ></rect>
                                    <line x1="3" y1="9" x2="21" y2="9"></line>
                                    <line x1="3" y1="15" x2="21" y2="15"></line>
                                  </svg>
                                  <span className="font-medium text-sm">
                                    Faktur Lebar
                                  </span>
                                </div>
                                <div className="w-full h-12 bg-white border border-gray-200 rounded-md flex items-center justify-center relative">
                                  <div className="w-full h-full p-2 flex flex-col justify-center">
                                    <div className="h-1.5 bg-gray-200 w-full mb-1 rounded"></div>
                                    <div className="flex justify-between items-center">
                                      <div className="w-1/3">
                                        <div className="h-1 bg-gray-300 w-full mb-0.5 rounded"></div>
                                        <div className="h-1 bg-gray-300 w-full mb-0.5 rounded"></div>
                                      </div>
                                      <div className="w-3/5">
                                        <div className="h-1 bg-gray-200 w-full mb-0.5 rounded"></div>
                                        <div className="h-1 bg-gray-200 w-full rounded"></div>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                                <div className="text-xs text-gray-500 mt-1.5">
                                  Kertas A4
                                </div>
                              </div>
                            </div>

                            <div
                              className={`border rounded-md p-3 cursor-pointer ${
                                selectedOrientation === "vertical"
                                  ? "border-blue-500 bg-blue-50 dark:bg-blue-900/20"
                                  : "hover:bg-gray-50 dark:hover:bg-gray-800"
                              }`}
                              onClick={() => {
                                setSelectedOrientation("vertical");
                                // Update template to match orientation
                                if (selectedTemplate.startsWith("horizontal")) {
                                  const styleNumber = selectedTemplate.replace(
                                    "horizontal",
                                    ""
                                  );
                                  setSelectedTemplate(`vertical${styleNumber}`);
                                }
                              }}
                            >
                              <div className="text-center">
                                <div className="flex items-center justify-center mb-2">
                                  <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    width="20"
                                    height="20"
                                    viewBox="0 0 24 24"
                                    fill="none"
                                    stroke="currentColor"
                                    strokeWidth="2"
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    className="mr-1.5 text-purple-600"
                                  >
                                    <rect
                                      x="3"
                                      y="3"
                                      width="18"
                                      height="18"
                                      rx="2"
                                      ry="2"
                                    ></rect>
                                    <line x1="8" y1="3" x2="8" y2="21"></line>
                                    <line x1="16" y1="3" x2="16" y2="21"></line>
                                  </svg>
                                  <span className="font-medium text-sm">
                                    Faktur Kecil
                                  </span>
                                </div>
                                <div className="w-full h-12 bg-white border border-gray-200 rounded-md flex items-center justify-center relative">
                                  <div className="w-full h-full p-2 flex flex-col justify-center">
                                    <div className="h-1.5 bg-gray-200 w-full mb-1 rounded"></div>
                                    <div className="flex flex-col items-center">
                                      <div className="w-3/4">
                                        <div className="h-1 bg-gray-300 w-full mb-0.5 rounded"></div>
                                        <div className="h-1 bg-gray-300 w-full mb-0.5 rounded"></div>
                                      </div>
                                      <div className="w-full">
                                        <div className="h-1 bg-gray-200 w-full rounded"></div>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                                <div className="text-xs text-gray-500 mt-1.5">
                                  Thermal Printer (58mm/80mm)
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>

                        {/* Template Style Options */}
                        <div className="space-y-3">
                          <div className="font-medium">Gaya Template</div>
                          <Select
                            value={selectedTemplate}
                            onValueChange={(value) => {
                              setSelectedTemplate(value);
                            }}
                          >
                            <SelectTrigger className="w-full">
                              <SelectValue placeholder="Pilih template" />
                            </SelectTrigger>
                            <SelectContent>
                              {selectedOrientation === "horizontal" ? (
                                <SelectGroup>
                                  <SelectLabel>
                                    Template Faktur Lebar (A4)
                                  </SelectLabel>
                                  <SelectItem value="horizontal1">
                                    Template 1
                                  </SelectItem>
                                  <SelectItem value="horizontal2">
                                    Template 2
                                  </SelectItem>
                                  <SelectItem value="horizontal3">
                                    Template 3
                                  </SelectItem>
                                </SelectGroup>
                              ) : (
                                <SelectGroup>
                                  <SelectLabel>
                                    Template Faktur Kecil (Thermal)
                                  </SelectLabel>
                                  <SelectItem value="vertical1">
                                    Template 1
                                  </SelectItem>
                                  <SelectItem value="vertical2">
                                    Template 2
                                  </SelectItem>
                                  <SelectItem value="vertical3">
                                    Template 3
                                  </SelectItem>
                                </SelectGroup>
                              )}
                            </SelectContent>
                          </Select>
                        </div>

                        {/* Action Buttons */}
                        <div className="mt-auto pt-6">
                          <div className="flex flex-col gap-3">
                            <Button onClick={handlePrint} className="w-full">
                              Cetak Sekarang
                            </Button>
                            <Button
                              variant="outline"
                              onClick={() => setIsPrintDialogOpen(false)}
                              className="w-full"
                            >
                              Batal
                            </Button>
                          </div>
                        </div>
                      </div>

                      {/* Right Column - Preview */}
                      <div className="border rounded-md p-4 overflow-hidden">
                        <div className="font-medium mb-3 flex items-center">
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            width="18"
                            height="18"
                            viewBox="0 0 24 24"
                            fill="none"
                            stroke="currentColor"
                            strokeWidth="2"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            className="mr-2 text-gray-500"
                          >
                            <path d="M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z"></path>
                            <circle cx="12" cy="12" r="3"></circle>
                          </svg>
                          Pratinjau Template
                        </div>
                        <div
                          className={`${
                            selectedOrientation === "horizontal"
                              ? "aspect-[16/9]"
                              : "aspect-[3/4]"
                          } bg-gray-100 rounded-md flex items-center justify-center overflow-hidden border border-dashed border-gray-300 min-h-[400px] max-w-full`}
                        >
                          {/* Image placeholder for template preview */}
                          <div className="w-full h-full relative flex flex-col items-center justify-center overflow-hidden">
                            {/* Template image will be placed here */}
                            <div className="text-gray-400 flex flex-col items-center">
                              <svg
                                xmlns="http://www.w3.org/2000/svg"
                                width="64"
                                height="64"
                                viewBox="0 0 24 24"
                                fill="none"
                                stroke="currentColor"
                                strokeWidth="1.5"
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                className="mb-3"
                              >
                                <rect
                                  x="3"
                                  y="3"
                                  width="18"
                                  height="18"
                                  rx="2"
                                  ry="2"
                                ></rect>
                                <circle cx="8.5" cy="8.5" r="1.5"></circle>
                                <polyline points="21 15 16 10 5 21"></polyline>
                              </svg>
                              <span className="text-base">
                                Pratinjau Template
                              </span>
                              <span className="text-sm mt-1">
                                {selectedOrientation === "horizontal"
                                  ? "Faktur Lebar (Kertas A4)"
                                  : "Faktur Kecil (Thermal Printer 58mm/80mm)"}
                              </span>
                            </div>

                            {/* Overlay with template name */}
                            <div className="absolute bottom-0 left-0 right-0 bg-black bg-opacity-60 text-white p-2 text-center">
                              <div className="font-medium text-sm">
                                {selectedTemplate.includes("horizontal1") &&
                                  "Template 1"}
                                {selectedTemplate.includes("horizontal2") &&
                                  "Template 2"}
                                {selectedTemplate.includes("horizontal3") &&
                                  "Template 3"}
                                {selectedTemplate.includes("horizontal4") &&
                                  "Template 4"}
                                {selectedTemplate.includes("vertical1") &&
                                  "Template 1"}
                                {selectedTemplate.includes("vertical2") &&
                                  "Template 2"}
                                {selectedTemplate.includes("vertical3") &&
                                  "Template 3"}
                                {selectedTemplate.includes("vertical4") &&
                                  "Template 4"}
                              </div>
                            </div>
                          </div>
                        </div>
                        <div className="mt-3 text-center">
                          <div className="font-bold">
                            {selectedOrientation === "horizontal"
                              ? "Faktur Lebar"
                              : "Faktur Kecil"}{" "}
                            -{selectedTemplate.includes("1") && " Template 1"}
                            {selectedTemplate.includes("2") && " Template 2"}
                            {selectedTemplate.includes("3") && " Template 3"}
                            {selectedTemplate.includes("4") && " Template 4"}
                          </div>
                          <div className="text-sm text-gray-500 mt-1">
                            Gambar pratinjau akan ditampilkan di sini
                          </div>
                        </div>
                      </div>
                    </div>
                  </DialogContent>
                </Dialog>

                <Button variant="outline" asChild>
                  <Link
                    href={`/dashboard/purchases/edit/${purchase.transactionNumber || purchase.id}`}
                  >
                    Edit Pembelian
                  </Link>
                </Button>
                <AlertDialog>
                  <AlertDialogTrigger asChild>
                    <Button variant="destructive" className="gap-2">
                      <Trash className="h-4 w-4" />
                      Hapus Pembelian
                    </Button>
                  </AlertDialogTrigger>
                  <AlertDialogContent>
                    <AlertDialogHeader>
                      <AlertDialogTitle>Konfirmasi Hapus</AlertDialogTitle>
                      <AlertDialogDescription>
                        Apakah Anda yakin ingin menghapus pembelian ini?
                        Tindakan ini tidak dapat dibatalkan dan akan mengurangi
                        stok produk.
                      </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                      <AlertDialogCancel>Batal</AlertDialogCancel>
                      <AlertDialogAction
                        onClick={handleDeletePurchase}
                        disabled={isDeleting}
                        className="bg-red-500 hover:bg-red-600"
                      >
                        {isDeleting ? "Menghapus..." : "Hapus"}
                      </AlertDialogAction>
                    </AlertDialogFooter>
                  </AlertDialogContent>
                </AlertDialog>
              </div>

              {/* Creation and Update Information */}
              <div className="mt-6 pt-4 border-t border-gray-200 dark:border-gray-700">
                <div className="grid grid-cols-2 gap-4 text-sm text-gray-500 dark:text-gray-400">
                  <div>
                    <span className="font-medium">Dibuat pada:</span>{" "}
                    {new Date(purchase.createdAt).toLocaleDateString("id-ID", {
                      day: "numeric",
                      month: "long",
                      year: "numeric",
                    })}
                  </div>
                  <div className="text-right">
                    <span className="font-medium">Terakhir diperbarui:</span>{" "}
                    {new Date(purchase.updatedAt).toLocaleDateString("id-ID", {
                      day: "numeric",
                      month: "long",
                      year: "numeric",
                    })}
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  );
};

export default PurchaseDetailPage;
