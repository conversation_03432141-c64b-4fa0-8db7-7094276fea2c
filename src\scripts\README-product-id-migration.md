# Product ID Migration Scripts

These scripts are used to update the product ID format from `PRD-000000` to `PRD-{idCompany}-{year}-{sequentialNumber}` where:

1. `PRD-` is the fixed prefix for all product IDs
2. `{idCompany}` is the company ID of the user (e.g., IP000001)
3. `{year}` is the current year (e.g., 2024)
4. `{sequentialNumber}` is a 6-digit sequential number starting from 000001 for each company per year

## Prerequisites

Before running these scripts, ensure that:

1. You have updated the `generateProductId` function in `src/lib/generate-id.ts` to use the new format
2. All users have a company ID assigned (you can run `bun src/scripts/assign-company-ids.ts` if needed)
3. You have a backup of your database in case something goes wrong

## Migration Process

Follow these steps to safely migrate your product IDs:

### Step 1: Test the migration on a small sample

Run the test script to update a small sample of products (max 3) for a single user:

```bash
bun src/scripts/update-product-ids-test.ts
```

This script will:

- Select a single user with products and a company ID
- Update up to 3 products for that user to the new format
- Show the before and after product IDs
- Update all related references (variants, purchase items, sale items)

Verify that the products and their relationships are still working correctly in the application.

### Step 2: Run the full migration

Once you've confirmed the test was successful, run the full migration script:

```bash
bun src/scripts/update-product-ids.ts
```

This script will:

- Process all users with products
- Skip users without a company ID
- Update all products for each user to the new format
- Process products in batches to avoid overloading the database
- Update all related references (variants, purchase items, sale items)

### Step 3: Verify the migration

After running the migration, verify that:

- All product IDs now follow the new format
- Products are still accessible in the application
- Purchase and sale records still show the correct products
- New products are created with the new ID format

## Rollback Process

If you encounter issues after the migration, you can roll back to the old format:

```bash
bun src/scripts/rollback-product-ids.ts
```

This script will:

- Find all products with the new format (containing company ID and year in the ID)
- Revert them back to the old format (`PRD-000000`)
- Update all related references

## Troubleshooting

If you encounter any issues:

1. Check the console output for error messages
2. Verify that all users have a company ID
3. If specific products are causing problems, you can manually update them in the database
4. If the rollback script fails, you may need to restore from a backup

## Notes

- These scripts use transactions to ensure data integrity
- They process products in batches to avoid long-running transactions
- The migration preserves the original creation order of products
- Each company will have its own sequence starting from 000001 for each year
