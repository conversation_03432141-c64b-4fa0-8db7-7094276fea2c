"use client";

import type { NextPage } from "next";
import Head from "next/head";
import {
  ChevronUpIcon,
  ChevronDownIcon,
  ArrowsUpDownIcon,
} from "@heroicons/react/24/outline";
import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { Ta<PERSON>, Ta<PERSON><PERSON>ist, Ta<PERSON>Trigger, TabsContent } from "@/components/ui/tabs";
import { toast } from "sonner";
import { Pagination } from "@/components/ui/pagination";
import { deleteSale } from "@/actions/entities/sales";

// Import types and components
import { Sale, SaleCounts, ColumnVisibility } from "./types";
import { SaleSummaryCards } from "./components/SaleSummaryCards";
import { SaleActions } from "./components/SaleActions";
import { SaleTableDesktop } from "./components/SaleTableDesktop";
import { WarehouseTabContent } from "./components/WarehouseTabContent";

interface SalesPageProps {
  sales: Sale[];
}

const SalesPage: NextPage<SalesPageProps> = ({ sales }) => {
  const router = useRouter();
  const [searchTerm, setSearchTerm] = useState("");
  const [mainTab, setMainTab] = useState("sales");
  const [subTab, setSubTab] = useState("all-sales");
  const [filteredSales, setFilteredSales] = useState<Sale[]>(sales);
  const [sortField, setSortField] = useState<string | null>(null);
  const [sortDirection, setSortDirection] = useState<"asc" | "desc">("asc");
  const [selectedSales, setSelectedSales] = useState<string[]>([]);

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [paginatedSales, setPaginatedSales] = useState<Sale[]>([]);

  // Column visibility state with localStorage persistence
  const [columnVisibility, setColumnVisibility] = useState<ColumnVisibility>(
    () => {
      // Try to get saved column visibility from localStorage
      if (typeof window !== "undefined") {
        const savedVisibility = localStorage.getItem("salesColumnVisibility");
        if (savedVisibility) {
          try {
            return JSON.parse(savedVisibility) as ColumnVisibility;
          } catch (error) {
            console.error("Failed to parse saved column visibility:", error);
          }
        }
      }

      // Default column visibility if nothing in localStorage
      return {
        id: true,
        date: true,
        paymentDueDate: false,
        customer: false,
        totalAmount: true,
        itemCount: true,
        invoiceRef: false,
        tags: false,
      };
    }
  );

  // Calculate sale counts for the summary cards
  const saleCounts: SaleCounts = {
    total: sales.length,
    today: sales.filter((s) => {
      const now = new Date();
      const saleDate = new Date(s.saleDate);
      return (
        saleDate.getDate() === now.getDate() &&
        saleDate.getMonth() === now.getMonth() &&
        saleDate.getFullYear() === now.getFullYear()
      );
    }).length,
    thisMonth: sales.filter((s) => {
      const now = new Date();
      const saleDate = new Date(s.saleDate);
      return (
        saleDate.getMonth() === now.getMonth() &&
        saleDate.getFullYear() === now.getFullYear()
      );
    }).length,
    pending: 0, // This would normally be calculated from the data
    drafts: sales.filter((s) => s.isDraft).length,
  };

  // Filter sales based on search term and tab
  useEffect(() => {
    // Reset to first page when search term changes
    setCurrentPage(1);

    // Start with all sales
    let filtered = [...sales];

    // Apply sub-tab filters
    if (subTab === "all-sales") {
      // Filter out draft sales from all-sales tab
      filtered = filtered.filter((sale) => !sale.isDraft);
    } else if (subTab === "drafts") {
      // Show only draft sales
      filtered = filtered.filter((sale) => sale.isDraft);
    }

    // Apply search term filter
    if (searchTerm.trim()) {
      const lowercasedTerm = searchTerm.toLowerCase();
      filtered = filtered.filter((sale) => {
        return (
          sale.id.toLowerCase().includes(lowercasedTerm) ||
          (sale.transactionNumber &&
            sale.transactionNumber.toLowerCase().includes(lowercasedTerm))
        );
      });
    }

    setFilteredSales(filtered);
  }, [searchTerm, sales, subTab]);

  // Apply pagination to filtered sales
  useEffect(() => {
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    setPaginatedSales(filteredSales.slice(startIndex, endIndex));
  }, [filteredSales, currentPage, itemsPerPage]);

  // Save column visibility to localStorage whenever it changes
  useEffect(() => {
    if (typeof window !== "undefined") {
      localStorage.setItem(
        "salesColumnVisibility",
        JSON.stringify(columnVisibility)
      );
    }
  }, [columnVisibility]);

  // Sort sales
  const handleSort = (field: string) => {
    // Reset to first page when sorting changes
    setCurrentPage(1);

    if (sortField === field) {
      // Toggle sort direction if clicking the same field
      setSortDirection(sortDirection === "asc" ? "desc" : "asc");
    } else {
      // Set new sort field and default to ascending
      setSortField(field);
      setSortDirection("asc");
    }

    // Sort the filtered sales
    const sortedSales = [...filteredSales].sort((a, b) => {
      let aValue, bValue;

      // Handle special case for itemCount which is derived
      if (field === "itemCount") {
        aValue = a.items.length;
        bValue = b.items.length;
      } else {
        // Handle other fields
        aValue = a[field as keyof Sale] || "";
        bValue = b[field as keyof Sale] || "";
      }

      // Compare values
      if (aValue < bValue) return sortDirection === "asc" ? -1 : 1;
      if (aValue > bValue) return sortDirection === "asc" ? 1 : -1;
      return 0;
    });

    setFilteredSales(sortedSales);
  };

  // Get sort icon based on current sort state
  const getSortIcon = (field: string) => {
    if (sortField !== field) {
      return <ArrowsUpDownIcon className="h-4 w-4 ml-1" />;
    }
    return sortDirection === "asc" ? (
      <ChevronUpIcon className="h-4 w-4 ml-1" />
    ) : (
      <ChevronDownIcon className="h-4 w-4 ml-1" />
    );
  };

  // Handle batch delete of selected sales
  const handleBatchDelete = async () => {
    if (selectedSales.length === 0) return;

    try {
      // Delete each selected sale
      for (const id of selectedSales) {
        const result = await deleteSale(id);
        if (result.error) {
          toast.error(`Gagal menghapus penjualan: ${result.error}`);
        }
      }

      // Show success message
      toast.success(`${selectedSales.length} penjualan berhasil dihapus`);

      // Clear selection
      setSelectedSales([]);

      // Refresh the page
      router.refresh();
    } catch (error) {
      console.error("Error deleting sales:", error);
      toast.error("Terjadi kesalahan saat menghapus penjualan.");
    }
  };

  return (
    <>
      <Head>
        <title>Penjualan - Kasir Online</title>
      </Head>

      <div className="space-y-6">
        {/* Main Tabs */}
        <Tabs
          defaultValue="sales"
          value={mainTab}
          onValueChange={setMainTab}
          className="w-full"
        >
          <TabsList className="mb-4">
            <TabsTrigger value="sales">Penjualan</TabsTrigger>
            <TabsTrigger value="warehouse">Gudang</TabsTrigger>
          </TabsList>

          <TabsContent value="sales" className="space-y-6">
            {/* Sale Status Summary Cards */}
            <SaleSummaryCards saleCounts={saleCounts} />

            {/* Sub Tabs */}
            <Tabs
              defaultValue="all-sales"
              value={subTab}
              onValueChange={setSubTab}
              className="w-full"
            >
              <TabsList className="mb-4">
                <TabsTrigger value="all-sales">Daftar Penjualan</TabsTrigger>
                <TabsTrigger value="drafts">Draf Penjualan</TabsTrigger>
              </TabsList>

              <TabsContent value="all-sales" className="space-y-6">
                {/* Header Actions */}
                <SaleActions
                  columnVisibility={columnVisibility}
                  setColumnVisibility={setColumnVisibility}
                  searchTerm={searchTerm}
                  setSearchTerm={setSearchTerm}
                  onFilterClick={() =>
                    toast.info("Fitur filter akan segera hadir!")
                  }
                  onImportClick={() =>
                    toast.info("Fitur import akan segera hadir!")
                  }
                  onExportClick={() =>
                    toast.info("Fitur export akan segera hadir!")
                  }
                  selectedSales={selectedSales}
                  onBatchDelete={handleBatchDelete}
                />

                {/* Sales List */}
                <div className="overflow-x-auto">
                  {/* Table View */}
                  <SaleTableDesktop
                    sales={paginatedSales}
                    columnVisibility={columnVisibility}
                    handleSort={handleSort}
                    getSortIcon={getSortIcon}
                    searchTerm={searchTerm}
                    selectedSales={selectedSales}
                    setSelectedSales={setSelectedSales}
                  />
                </div>

                {/* Pagination - Moved outside the overflow container */}
                <div className="mt-4">
                  <Pagination
                    currentPage={currentPage}
                    totalPages={Math.ceil(filteredSales.length / itemsPerPage)}
                    onPageChange={setCurrentPage}
                    itemsPerPage={itemsPerPage}
                    onItemsPerPageChange={setItemsPerPage}
                    totalItems={filteredSales.length}
                  />
                </div>
              </TabsContent>

              <TabsContent value="drafts" className="space-y-6">
                {/* Header Actions */}
                <SaleActions
                  columnVisibility={columnVisibility}
                  setColumnVisibility={setColumnVisibility}
                  searchTerm={searchTerm}
                  setSearchTerm={setSearchTerm}
                  onFilterClick={() =>
                    toast.info("Fitur filter akan segera hadir!")
                  }
                  onImportClick={() =>
                    toast.info("Fitur import akan segera hadir!")
                  }
                  onExportClick={() =>
                    toast.info("Fitur export akan segera hadir!")
                  }
                  selectedSales={selectedSales}
                  onBatchDelete={handleBatchDelete}
                />

                {/* Sales List */}
                <div className="overflow-x-auto">
                  {/* Table View */}
                  <SaleTableDesktop
                    sales={paginatedSales.filter((s) => s.isDraft)}
                    columnVisibility={columnVisibility}
                    handleSort={handleSort}
                    getSortIcon={getSortIcon}
                    searchTerm={searchTerm}
                    selectedSales={selectedSales}
                    setSelectedSales={setSelectedSales}
                  />
                </div>

                {/* Pagination - Moved outside the overflow container */}
                <div className="mt-4">
                  <Pagination
                    currentPage={currentPage}
                    totalPages={Math.ceil(
                      filteredSales.filter((s) => s.isDraft).length /
                        itemsPerPage
                    )}
                    onPageChange={setCurrentPage}
                    itemsPerPage={itemsPerPage}
                    onItemsPerPageChange={setItemsPerPage}
                    totalItems={filteredSales.filter((s) => s.isDraft).length}
                  />
                </div>
              </TabsContent>
            </Tabs>
          </TabsContent>

          <TabsContent value="warehouse" className="space-y-6">
            <WarehouseTabContent />
          </TabsContent>
        </Tabs>
      </div>
    </>
  );
};

export default SalesPage;
