"use client";

import React from "react";
import { Tag } from "lucide-react";
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";

interface Category {
  id: string;
  name: string;
}

interface ProductDetailsTabProps {
  id: string; // Add product ID
  name: string;
  sku: string | null;
  barcode?: string;
  taxRate?: number;
  description: string | null;
  tags?: string[];
}

const ProductDetailsTab: React.FC<ProductDetailsTabProps> = ({
  id,
  name,
  sku,
  barcode,
  taxRate,
  description,
  tags,
}) => {
  return (
    <div className="space-y-6">
      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-lg">Informasi Dasar</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 gap-y-4">
            <div>
              <p className="text-sm font-medium text-muted-foreground">
                ID Produk
              </p>
              <p className="font-mono">{id}</p>
            </div>
            <div>
              <p className="text-sm font-medium text-muted-foreground">
                Nama Produk
              </p>
              <p>{name}</p>
            </div>
            <div>
              <p className="text-sm font-medium text-muted-foreground">SKU</p>
              <p>{sku || "-"}</p>
            </div>
            {barcode && (
              <div>
                <p className="text-sm font-medium text-muted-foreground">
                  Barcode
                </p>
                <p>{barcode}</p>
              </div>
            )}
            {taxRate !== undefined && (
              <div>
                <p className="text-sm font-medium text-muted-foreground">
                  Pajak
                </p>
                <p>{taxRate}%</p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Description Card */}
      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-lg">Deskripsi</CardTitle>
        </CardHeader>
        <CardContent>
          {description ? (
            <p className="whitespace-pre-line">{description}</p>
          ) : (
            <p className="text-muted-foreground italic">Tidak ada deskripsi</p>
          )}
        </CardContent>
      </Card>

      {/* Tags Card */}
      {tags && tags.length > 0 && (
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg">Tag</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-wrap gap-2">
              {tags.map((tag, index) => (
                <Badge
                  key={index}
                  variant="outline"
                  className="flex items-center gap-1"
                >
                  <Tag className="h-3 w-3" />
                  {tag}
                </Badge>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default ProductDetailsTab;
