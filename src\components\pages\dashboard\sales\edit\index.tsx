"use client";

import React, { useState, useEffect, useTransition } from "react";
import { useRouter } from "next/navigation";
import { useForm, useFieldArray } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { toast } from "sonner";
import { updateSale, getNextTransactionNumber } from "@/actions/entities/sales";
import { Form } from "@/components/ui/form";
import { Button } from "@/components/ui/button";
import Link from "next/link";
import { SaleFormValues, Product, EnhancedSaleSchema } from "../new/types";
import CombinedSaleForm from "../new/components/CombinedSaleForm";
import { ArrowLeft, Check } from "lucide-react";

// Define the Sale interface
interface SaleItem {
  id: string;
  quantity: number;
  priceAtSale: number;
  productId: string;
  product: Product;
  unit?: string;
  tax?: string;
}

interface Sale {
  id: string;
  totalAmount: number;
  saleDate: string;
  items: SaleItem[];
  priceIncludesTax?: boolean;
  createdAt?: string;
  customerEmail?: string;
  customerRefNumber?: string;
  shippingAddress?: string;
  paymentDueDate?: string;
  paymentTerms?: string;
  warehouse?: string;
  tags?: string[];
  memo?: string;
  lampiran?: { url: string; filename: string }[];
  transactionNumber?: string;
  invoiceRef?: string;
}

// Props use imported types
interface EnhancedSaleEditPageProps {
  sale: Sale;
  products: Product[];
}

const EnhancedSaleEditPage: React.FC<EnhancedSaleEditPageProps> = ({
  sale,
  products,
}) => {
  const router = useRouter();
  const [isPending, startTransition] = useTransition();
  const [totalAmount, setTotalAmount] = useState<number>(sale.totalAmount);

  // Initialize the form with enhanced schema and sale data
  const form = useForm<SaleFormValues>({
    resolver: zodResolver(EnhancedSaleSchema),
    defaultValues: {
      items: sale.items.map((item) => ({
        productId: item.productId,
        quantity: item.quantity,
        priceAtSale: item.priceAtSale,
        unit: item.unit || "Buah", // Default to "Buah" if not provided
        tax: item.tax || "", // Default to empty string if not provided
      })),
      totalAmount: sale.totalAmount,
      customerId: "default", // Default to general customer
      customerEmail: sale.customerEmail || "",
      customerNIK: "",
      customerNPWP: "",
      paymentMethod: "cash",
      amountPaid: 0,
      notes: "",
      // New fields
      transactionDate: new Date(sale.saleDate),
      transactionNumber: sale.transactionNumber || "",
      customerRefNumber: sale.customerRefNumber || "",
      shippingAddress: sale.shippingAddress || "",
      paymentDueDate: sale.paymentDueDate
        ? new Date(sale.paymentDueDate)
        : undefined,
      paymentTerms: sale.paymentTerms || "Net 30",
      warehouse: sale.warehouse || "",
      tags: sale.tags || [],
      memo: sale.memo || "",
      lampiran: sale.lampiran || [],
      priceIncludesTax: sale.priceIncludesTax || false,
    },
  });

  // Get the items field array
  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: "items",
  });

  // Watch for changes in the items array to calculate the total
  const items = form.watch("items");

  // Calculate total amount whenever items change
  useEffect(() => {
    const total = items.reduce(
      (sum: number, item: SaleFormValues["items"][number]) => {
        const quantity = item?.quantity ?? 0;
        const price = item?.priceAtSale ?? 0;
        return sum + quantity * price;
      },
      0
    );
    setTotalAmount(total);
    form.setValue("totalAmount", total);
  }, [items, form]);

  // Handle product selection
  const handleProductChange = (index: number, productId: string) => {
    const selectedProduct: Product | undefined = products.find(
      (p) => p.id === productId
    );
    // Check if selectedProduct exists and its price is a number
    if (selectedProduct && typeof selectedProduct.price === "number") {
      const priceValue = selectedProduct.price; // Assign to variable
      form.setValue(`items.${index}.priceAtSale`, priceValue);

      // Force recalculation of total immediately
      const currentItems = form.getValues("items");

      // Ensure the item exists before trying to update it
      if (currentItems[index]) {
        currentItems[index].priceAtSale = priceValue;
      }

      const total = currentItems.reduce(
        (sum: number, item: SaleFormValues["items"][number]) => {
          const quantity = item?.quantity ?? 0;
          const price = item?.priceAtSale ?? 0;
          return sum + quantity * price;
        },
        0
      );

      setTotalAmount(total);
      form.setValue("totalAmount", total);
    }
  };

  // Handle form submission
  const onSubmit = (values: SaleFormValues) => {
    startTransition(async () => {
      try {
        // Generate auto values if needed
        let autoTransactionNumber = values.transactionNumber;
        let autoInvoiceRef = values.invoiceRef;

        // If transaction number is empty, generate one using the sale's createdAt date
        if (!values.transactionNumber) {
          const trxResult = await getNextTransactionNumber(
            "TRX",
            sale.createdAt
          );
          if (trxResult.success) {
            autoTransactionNumber = trxResult.nextNumber;
          }
        }

        // If invoice reference is empty, generate one using the sale's createdAt date
        if (!values.invoiceRef) {
          const invResult = await getNextTransactionNumber(
            "INV",
            sale.createdAt
          );
          if (invResult.success) {
            autoInvoiceRef = invResult.nextNumber;
          }
        }

        // Extract the fields for the sale update
        const saleData = {
          items: values.items,
          totalAmount: values.totalAmount,
          customerId: values.customerId,
          customerEmail: values.customerEmail,
          customerRefNumber: values.customerRefNumber,
          shippingAddress: values.shippingAddress,
          transactionDate: values.transactionDate,
          transactionNumber: autoTransactionNumber,
          invoiceRef: autoInvoiceRef,
          paymentDueDate: values.paymentDueDate,
          paymentTerms: values.paymentTerms,
          warehouse: values.warehouse,
          tags: values.tags,
          memo: values.memo,
          lampiran: values.lampiran,
          priceIncludesTax: values.priceIncludesTax,
        };

        const result = await updateSale(sale.id, saleData);
        if (result.success) {
          toast.success(result.success);
          // Redirect after a short delay
          router.push(
            `/dashboard/sales/detail/${autoTransactionNumber || sale.id}`
          );
        } else if (result.error) {
          toast.error(result.error);
        } else {
          toast.error("Terjadi kesalahan yang tidak diketahui.");
        }
      } catch (error) {
        console.error("Submit Error:", error);
        toast.error("Gagal menghubungi server.");
      }
    });
  };

  return (
    <div className="w-full px-4 py-6">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h1 className="text-2xl font-bold">Edit Penjualan</h1>
          <p className="text-muted-foreground">
            Perbarui transaksi penjualan dengan mengisi detail di bawah ini
          </p>
        </div>
        <Button variant="outline" asChild className="gap-2">
          <Link
            href={`/dashboard/sales/detail/${sale.transactionNumber || sale.id}`}
          >
            <ArrowLeft className="h-4 w-4" />
            Kembali
          </Link>
        </Button>
      </div>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)}>
          <div className="space-y-6">
            {/* Main Form Content */}
            <CombinedSaleForm
              control={form.control}
              isPending={isPending}
              products={products}
              items={items}
              fields={fields}
              append={append}
              remove={remove}
              handleProductChange={handleProductChange}
              totalAmount={totalAmount}
              createdAt={sale.createdAt}
            />
          </div>

          {/* Form Actions */}
          <div className="mt-8 pt-6 border-t border-gray-200 dark:border-gray-700 flex justify-end gap-4">
            <Button
              type="button"
              variant="outline"
              asChild
              disabled={isPending}
            >
              <Link
                href={`/dashboard/sales/detail/${sale.transactionNumber || sale.id}`}
              >
                Batal
              </Link>
            </Button>
            <Button type="submit" disabled={isPending} className="gap-2">
              {isPending ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  <span>Menyimpan...</span>
                </>
              ) : (
                <>
                  <Check className="h-4 w-4" />
                  <span>Simpan Perubahan</span>
                </>
              )}
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
};

export default EnhancedSaleEditPage;
