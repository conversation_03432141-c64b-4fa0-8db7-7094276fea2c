"use client";

import { deleteEmployee, getEmployees } from "@/actions/entities/employee";
import { Role } from "@prisma/client";
import { useEffect, useState } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { toast } from "sonner";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON>L<PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { UserPlus, Users, AlertCircle } from "lucide-react";
import { EmployeeTable } from "./components/employee-table";
import { AddEmployeeDialog } from "./components/add-employee-dialog";
import { EditEmployeeNameDialog } from "./components/edit-employee-name-dialog";
import { EditEmployeePasswordDialog } from "./components/edit-employee-password-dialog";

interface Employee {
  id: string;
  name: string;
  employeeId: string;
  role: Role;
  createdAt?: Date; // Removed duplicate
}

export default function EmployeeManagement() {
  // const { data: session } = useSession(); // Removed as it wasn't used
  const [employees, setEmployees] = useState<Employee[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditNameDialogOpen, setIsEditNameDialogOpen] = useState(false);
  const [isEditPasswordDialogOpen, setIsEditPasswordDialogOpen] =
    useState(false);
  const [selectedEmployee, setSelectedEmployee] = useState<Employee | null>(
    null
  );

  useEffect(() => {
    fetchEmployees();
  }, []);

  const fetchEmployees = async () => {
    setLoading(true);
    try {
      const result = await getEmployees();
      if (result.error) {
        setError(result.error);
      } else if (result.employees) {
        setEmployees(result.employees);
      }
    } catch (err) {
      setError("Terjadi kesalahan saat mengambil data karyawan");
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  // Keep handleDeleteEmployee
  const handleDeleteEmployee = async (id: string) => {
    if (confirm("Apakah Anda yakin ingin menghapus karyawan ini?")) {
      try {
        const result = await deleteEmployee(id);

        if (result.error) {
          toast.error(result.error);
        } else {
          toast.success(result.success);
          fetchEmployees();
        }
      } catch (err) {
        toast.error("Terjadi kesalahan saat menghapus karyawan");
        console.error(err);
      }
    }
  };

  // Remove resetForm, resetEditNameForm, resetEditPasswordForm (handled in dialogs)

  // Keep handleEditNameClick and handleEditPasswordClick
  const handleEditNameClick = (employee: Employee) => {
    setSelectedEmployee(employee);
    // setEditName(employee.name); // Removed, state handled in dialog
    // setEditRole(employee.role === Role.ADMIN ? "ADMIN" : "CASHIER"); // Removed, state handled in dialog
    setIsEditNameDialogOpen(true);
  };

  const handleEditPasswordClick = (employee: Employee) => {
    setSelectedEmployee(employee);
    // No need to reset password fields here, handled in dialog
    setIsEditPasswordDialogOpen(true);
  };

  // Remove handleUpdateEmployeeName and handleUpdateEmployeePassword (handled in dialogs)

  // Loading state with skeleton effect
  if (loading && employees.length === 0) {
    return (
      <div className="space-y-6">
        {/* Skeleton for Summary Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {[1, 2, 3].map((i) => (
            <div
              key={i}
              className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 overflow-hidden shadow-sm"
            >
              <div className="px-6 py-5">
                <div className="h-6 w-40 bg-gray-200 dark:bg-gray-700 rounded animate-pulse mb-4"></div>
                <div className="h-8 w-16 bg-gray-300 dark:bg-gray-600 rounded animate-pulse"></div>
                <div className="h-4 w-24 bg-gray-200 dark:bg-gray-700 rounded animate-pulse mt-2"></div>
              </div>
            </div>
          ))}
        </div>

        {/* Skeleton for Main Content */}
        <div className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 overflow-hidden shadow-sm">
          <div className="px-6 py-5 border-b border-gray-200 dark:border-gray-700">
            <div className="flex justify-between items-center">
              <div>
                <div className="h-6 w-48 bg-gray-200 dark:bg-gray-700 rounded animate-pulse mb-2"></div>
                <div className="h-4 w-64 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
              </div>
              <div className="h-10 w-36 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
            </div>
          </div>

          <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <div className="h-10 w-full max-w-md bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
          </div>

          <div className="p-6">
            <div className="rounded-md border border-gray-200 dark:border-gray-700 overflow-hidden">
              <div className="bg-gray-50 dark:bg-gray-800/50 p-4">
                <div className="grid grid-cols-5 gap-4">
                  {[1, 2, 3, 4, 5].map((i) => (
                    <div
                      key={i}
                      className="h-6 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"
                    ></div>
                  ))}
                </div>
              </div>

              {[1, 2, 3, 4].map((row) => (
                <div
                  key={row}
                  className="border-t border-gray-200 dark:border-gray-700 p-4"
                >
                  <div className="grid grid-cols-5 gap-4">
                    {[1, 2, 3, 4, 5].map((col) => (
                      <div
                        key={`${row}-${col}`}
                        className={`h-6 bg-gray-200 dark:bg-gray-700 rounded animate-pulse ${col === 5 ? "flex justify-end" : ""}`}
                        style={{ animationDelay: `${row * 0.1 + col * 0.05}s` }}
                      >
                        {col === 5 && (
                          <div className="flex space-x-2">
                            <div className="h-6 w-6 bg-gray-300 dark:bg-gray-600 rounded animate-pulse"></div>
                            <div className="h-6 w-6 bg-gray-300 dark:bg-gray-600 rounded animate-pulse"></div>
                            <div className="h-6 w-6 bg-gray-300 dark:bg-gray-600 rounded animate-pulse"></div>
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (error && employees.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center h-64 space-y-4 text-red-500">
        <AlertCircle className="h-8 w-8" />
        <p>{error}</p>
      </div>
    );
  }

  // Count employees by role
  const adminCount = employees.filter((e) => e.role === "ADMIN").length;
  const cashierCount = employees.filter((e) => e.role === "CASHIER").length;

  return (
    <div className="space-y-6">
      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card className="bg-gradient-to-br from-blue-50 to-white dark:from-blue-950/20 dark:to-gray-800 border-blue-100 dark:border-blue-900/30">
          <CardHeader className="pb-2">
            <CardTitle className="text-lg flex items-center gap-2">
              <Users className="h-5 w-5 text-blue-500 dark:text-blue-400" />
              Total Karyawan
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-blue-600 dark:text-blue-400">
              {employees.length}
            </div>
            <p className="text-sm text-muted-foreground mt-1">Karyawan aktif</p>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-purple-50 to-white dark:from-purple-950/20 dark:to-gray-800 border-purple-100 dark:border-purple-900/30">
          <CardHeader className="pb-2">
            <CardTitle className="text-lg flex items-center gap-2">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-5 w-5 text-purple-500 dark:text-purple-400"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              >
                <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2" />
                <circle cx="9" cy="7" r="4" />
                <path d="M22 21v-2a4 4 0 0 0-3-3.87" />
                <path d="M16 3.13a4 4 0 0 1 0 7.75" />
              </svg>
              Admin
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-purple-600 dark:text-purple-400">
              {adminCount}
            </div>
            <p className="text-sm text-muted-foreground mt-1">
              Dengan akses penuh
            </p>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-green-50 to-white dark:from-green-950/20 dark:to-gray-800 border-green-100 dark:border-green-900/30">
          <CardHeader className="pb-2">
            <CardTitle className="text-lg flex items-center gap-2">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-5 w-5 text-green-500 dark:text-green-400"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              >
                <path d="M20 12V8H6a2 2 0 0 1-2-2c0-1.1.9-2 2-2h12v4" />
                <path d="M4 6v12c0 1.1.9 2 2 2h14v-4" />
                <path d="M18 12a2 2 0 0 0-2 2c0 1.1.9 2 2 2h4v-4h-4z" />
              </svg>
              Kasir
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-green-600 dark:text-green-400">
              {cashierCount}
            </div>
            <p className="text-sm text-muted-foreground mt-1">
              Dengan akses terbatas
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <Card className="border-gray-200 dark:border-gray-700 shadow-sm">
        <CardHeader className="flex flex-row items-center justify-between pb-2 border-b border-gray-100 dark:border-gray-800">
          <div>
            <CardTitle className="text-xl">Manajemen Karyawan</CardTitle>
            <CardDescription>
              Kelola karyawan dan akses mereka ke sistem
            </CardDescription>
          </div>
          <Button
            className="flex items-center gap-2 bg-primary hover:bg-primary/90 text-white shadow-sm cursor-pointer"
            onClick={() => setIsAddDialogOpen(true)}
          >
            <UserPlus className="h-4 w-4" />
            <span>Tambah Karyawan</span>
          </Button>

          {/* Add Employee Dialog */}
          <AddEmployeeDialog
            open={isAddDialogOpen}
            onOpenChange={setIsAddDialogOpen}
            onSuccess={fetchEmployees} // Refresh list on success
          />
        </CardHeader>

        <Tabs defaultValue="all" className="w-full">
          <div className="px-6 pt-4 border-b border-gray-100 dark:border-gray-800">
            <TabsList className="grid w-full max-w-md grid-cols-3">
              <TabsTrigger value="all" className="rounded-md cursor-pointer">
                Semua
                <Badge
                  variant="secondary"
                  className="ml-2 bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-300"
                >
                  {employees.length}
                </Badge>
              </TabsTrigger>
              <TabsTrigger value="admin" className="rounded-md cursor-pointer">
                Admin
                <Badge
                  variant="secondary"
                  className="ml-2 bg-purple-100 dark:bg-purple-900/30 text-purple-600 dark:text-purple-300"
                >
                  {adminCount}
                </Badge>
              </TabsTrigger>
              <TabsTrigger
                value="cashier"
                className="rounded-md cursor-pointer"
              >
                Kasir
                <Badge
                  variant="secondary"
                  className="ml-2 bg-green-100 dark:bg-green-900/30 text-green-600 dark:text-green-300"
                >
                  {cashierCount}
                </Badge>
              </TabsTrigger>
            </TabsList>
          </div>

          <CardContent className="pt-6">
            <TabsContent value="all" className="mt-0">
              <EmployeeTable
                employees={employees}
                onEditName={handleEditNameClick}
                onEditPassword={handleEditPasswordClick}
                onDelete={handleDeleteEmployee}
              />
            </TabsContent>

            <TabsContent value="admin" className="mt-0">
              <EmployeeTable
                employees={employees.filter((e) => e.role === "ADMIN")}
                onEditName={handleEditNameClick}
                onEditPassword={handleEditPasswordClick}
                onDelete={handleDeleteEmployee}
              />
            </TabsContent>

            <TabsContent value="cashier" className="mt-0">
              <EmployeeTable
                employees={employees.filter((e) => e.role === "CASHIER")}
                onEditName={handleEditNameClick}
                onEditPassword={handleEditPasswordClick}
                onDelete={handleDeleteEmployee}
              />
            </TabsContent>
          </CardContent>
        </Tabs>
      </Card>

      {/* Edit Name Dialog */}
      <EditEmployeeNameDialog
        employee={selectedEmployee}
        open={isEditNameDialogOpen}
        onOpenChange={setIsEditNameDialogOpen}
        onSuccess={fetchEmployees} // Refresh list on success
      />

      {/* Edit Password Dialog */}
      <EditEmployeePasswordDialog
        employee={selectedEmployee}
        open={isEditPasswordDialogOpen}
        onOpenChange={setIsEditPasswordDialogOpen}
        // No onSuccess needed here as password change doesn't affect the list display
      />
    </div>
  );
}
