import React, { useState } from "react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { toast } from "sonner";
import { Product, ColumnVisibility } from "../types"; // Import from the new types file
import { Trash, LoaderCircle } from "lucide-react";
import { Button } from "@/components/ui/button";
import { deleteProduct } from "@/actions/entities/products";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";

interface ProductTableDesktopProps {
  products: Product[];
  columnVisibility: ColumnVisibility;
  handleSort: (field: string) => void;
  getSortIcon: (field: string) => React.ReactNode;
  getStockStatusBadge: (stock: number) => React.ReactNode;
  searchTerm: string;
}

export const ProductTableDesktop: React.FC<ProductTableDesktopProps> = ({
  products,
  columnVisibility,
  handleSort,
  getSortIcon,
  getStockStatusBadge,
  searchTerm,
}) => {
  const router = useRouter();
  const [deletingProductId, setDeletingProductId] = useState<string | null>(
    null
  );

  // Handle delete product
  const handleDeleteProduct = async (id: string) => {
    setDeletingProductId(id);
    try {
      const result = await deleteProduct(id);
      if (result.success) {
        toast.success(result.success);
        // Refresh the page to show updated data
        router.refresh();
      } else if (result.error) {
        toast.error(result.error);
      }
    } catch (error) {
      console.error("Error deleting product:", error);
      toast.error("Terjadi kesalahan saat menghapus produk.");
    } finally {
      setDeletingProductId(null);
    }
  };
  return (
    <div className="relative overflow-x-auto border border-gray-200 dark:border-gray-700 rounded-lg">
      <table className="w-full text-sm text-left text-gray-500 dark:text-gray-400">
        <thead className="text-xs text-gray-700 dark:text-gray-300 uppercase bg-gray-50 dark:bg-gray-700">
          <tr>
            {/* 1. Gambar Produk */}
            {columnVisibility.image && (
              <th
                scope="col"
                className="px-6 py-3 cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-600"
              >
                <div className="flex items-center">Gambar</div>
              </th>
            )}
            {/* 2. Nama Produk */}
            {columnVisibility.name && (
              <th
                scope="col"
                className="px-6 py-3 cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-600"
                onClick={() => handleSort("name")}
              >
                <div className="flex items-center">
                  Nama Produk
                  {getSortIcon("name")}
                </div>
              </th>
            )}
            {/* 3. Kode Produk */}
            {columnVisibility.sku && (
              <th
                scope="col"
                className="px-6 py-3 cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-600"
                onClick={() => handleSort("sku")}
              >
                <div className="flex items-center">
                  Kode Produk
                  {getSortIcon("sku")}
                </div>
              </th>
            )}
            {/* 4. Barcode */}
            {columnVisibility.barcode && (
              <th
                scope="col"
                className="px-6 py-3 cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-600"
                onClick={() => handleSort("barcode")}
              >
                <div className="flex items-center">
                  Barcode
                  {getSortIcon("barcode")}
                </div>
              </th>
            )}
            {/* 5. Satuan */}
            {columnVisibility.unit && (
              <th
                scope="col"
                className="px-6 py-3 cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-600"
                onClick={() => handleSort("unit")}
              >
                <div className="flex items-center">
                  Satuan
                  {getSortIcon("unit")}
                </div>
              </th>
            )}
            {/* 6. Total Stok */}
            {columnVisibility.stock && (
              <th
                scope="col"
                className="px-6 py-3 cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-600"
                onClick={() => handleSort("stock")}
              >
                <div className="flex items-center">
                  Total Stok
                  {getSortIcon("stock")}
                </div>
              </th>
            )}
            {/* 7. Harga Beli */}
            {columnVisibility.cost && (
              <th
                scope="col"
                className="px-6 py-3 cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-600"
                onClick={() => handleSort("cost")}
              >
                <div className="flex items-center">
                  Harga Beli
                  {getSortIcon("cost")}
                </div>
              </th>
            )}
            {/* 8. Harga Jual */}
            {columnVisibility.sellPrice && (
              <th
                scope="col"
                className="px-6 py-3 cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-600"
                onClick={() => handleSort("price")} // Assuming sellPrice sorts by price
              >
                <div className="flex items-center">
                  Harga Jual
                  {getSortIcon("price")}
                </div>
              </th>
            )}
            {/* 9. Harga Diskon */}
            {columnVisibility.discountPrice && (
              <th
                scope="col"
                className="px-6 py-3 cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-600"
                onClick={() => handleSort("discountPrice")} // Need a field for this if sorting is needed
              >
                <div className="flex items-center">
                  Harga Diskon
                  {getSortIcon("discountPrice")}
                </div>
              </th>
            )}
            {/* 10. Kategori Produk */}
            {columnVisibility.category && (
              <th
                scope="col"
                className="px-6 py-3 cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-600"
                onClick={() => handleSort("category")}
              >
                <div className="flex items-center">
                  Kategori Produk
                  {getSortIcon("category")}
                </div>
              </th>
            )}
            {/* 11. Tag Produk */}
            {columnVisibility.tags && (
              <th
                scope="col"
                className="px-6 py-3 cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-600"
                onClick={() => handleSort("tags")}
              >
                <div className="flex items-center">
                  Tag Produk
                  {getSortIcon("tags")}
                </div>
              </th>
            )}
            {/* 12. Varian Warna */}
            {columnVisibility.colorVariants && (
              <th
                scope="col"
                className="px-6 py-3 cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-600"
              >
                <div className="flex items-center">Varian Warna</div>
              </th>
            )}
            {/* 13. Status Stok */}
            {columnVisibility.stockStatus && (
              <th
                scope="col"
                className="px-6 py-3 cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-600"
              >
                <div className="flex items-center">Status Stok</div>
              </th>
            )}
            {/* Removed price column as it's redundant with sellPrice */}
            <th scope="col" className="px-6 py-3 text-right">
              <span className="sr-only">Aksi</span>
            </th>
          </tr>
        </thead>
        <tbody>
          {products.length > 0 ? (
            products.map((product) => (
              <tr
                key={product.id}
                className="bg-white dark:bg-gray-800 border-b dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700"
              >
                {/* 1. Gambar Produk */}
                {columnVisibility.image && (
                  <td className="px-6 py-4 whitespace-nowrap text-gray-500 dark:text-gray-400">
                    {product.image ? (
                      <div className="h-10 w-10 rounded-md overflow-hidden bg-gray-100 dark:bg-gray-700">
                        <img
                          src={product.image}
                          alt={product.name}
                          className="h-full w-full object-cover"
                        />
                      </div>
                    ) : (
                      <div className="h-10 w-10 rounded-md bg-gray-100 dark:bg-gray-700 flex items-center justify-center">
                        <span className="text-xs text-gray-400">No img</span>
                      </div>
                    )}
                  </td>
                )}
                {/* 2. Nama Produk */}
                {columnVisibility.name && (
                  <td className="px-6 py-4 font-medium text-gray-900 dark:text-gray-100 whitespace-nowrap">
                    <Link
                      href={`/dashboard/products/detail/${product.id}`}
                      className="hover:text-blue-600 text-blue-500 dark:hover:text-blue-400 cursor-pointer underline"
                    >
                      {product.name}
                    </Link>
                  </td>
                )}
                {/* 3. Kode Produk */}
                {columnVisibility.sku && (
                  <td className="px-6 py-4 whitespace-nowrap text-gray-500 dark:text-gray-400">
                    {product.sku || "-"}
                  </td>
                )}
                {/* 4. Barcode */}
                {columnVisibility.barcode && (
                  <td className="px-6 py-4 whitespace-nowrap text-gray-500 dark:text-gray-400">
                    {product.barcode || "-"}
                  </td>
                )}
                {/* 5. Satuan */}
                {columnVisibility.unit && (
                  <td className="px-6 py-4 whitespace-nowrap text-gray-500 dark:text-gray-400">
                    {product.unit || "Pcs"}
                  </td>
                )}
                {/* 6. Total Stok */}
                {columnVisibility.stock && (
                  <td className="px-6 py-4 whitespace-nowrap text-gray-500 dark:text-gray-400">
                    <span className="font-medium">{product.stock}</span>
                  </td>
                )}
                {/* 7. Harga Beli */}
                {columnVisibility.cost && (
                  <td className="px-6 py-4 whitespace-nowrap text-gray-500 dark:text-gray-400">
                    {product.cost
                      ? `Rp ${product.cost.toLocaleString("id-ID")}`
                      : "-"}
                  </td>
                )}
                {/* 8. Harga Jual */}
                {columnVisibility.sellPrice && (
                  <td className="px-6 py-4 whitespace-nowrap text-gray-500 dark:text-gray-400">
                    Rp {product.price.toLocaleString("id-ID")}
                  </td>
                )}
                {/* 9. Harga Diskon */}
                {columnVisibility.discountPrice && (
                  <td className="px-6 py-4 whitespace-nowrap text-gray-500 dark:text-gray-400">
                    {product.discountPrice ? (
                      <span className="text-green-600 dark:text-green-400">
                        Rp {product.discountPrice.toLocaleString("id-ID")}
                      </span>
                    ) : (
                      "-"
                    )}
                  </td>
                )}
                {/* 10. Kategori Produk */}
                {columnVisibility.category && (
                  <td className="px-6 py-4 whitespace-nowrap text-gray-500 dark:text-gray-400">
                    {product.category?.name || "-"}
                  </td>
                )}
                {/* 11. Tag Produk */}
                {columnVisibility.tags && (
                  <td className="px-6 py-4 whitespace-nowrap text-gray-500 dark:text-gray-400">
                    <div className="flex flex-wrap gap-1">
                      {product.tags && product.tags.length > 0 ? (
                        product.tags.map((tag, index) => (
                          <span
                            key={index}
                            className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300"
                          >
                            {typeof tag === "string" ? tag : tag.name}
                          </span>
                        ))
                      ) : (
                        <span key="no-tags">-</span>
                      )}
                    </div>
                  </td>
                )}
                {/* 12. Varian Warna */}
                {columnVisibility.colorVariants && (
                  <td className="px-6 py-4 whitespace-nowrap text-gray-500 dark:text-gray-400">
                    <div className="flex flex-wrap gap-1">
                      {product.hasVariants &&
                      product.variants &&
                      product.variants.length > 0 ? (
                        product.variants.map((variant, index) => (
                          <div key={index} className="flex items-center gap-1">
                            <div
                              className="w-4 h-4 rounded-full border"
                              style={{ backgroundColor: variant.colorCode }}
                            ></div>
                            <span className="text-xs">{variant.colorName}</span>
                          </div>
                        ))
                      ) : (
                        <span>-</span>
                      )}
                    </div>
                  </td>
                )}
                {/* 13. Status Stok */}
                {columnVisibility.stockStatus && (
                  <td className="px-6 py-4 whitespace-nowrap text-gray-500 dark:text-gray-400">
                    {getStockStatusBadge(product.stock)}
                  </td>
                )}
                <td className="px-6 py-4 text-right whitespace-nowrap">
                  <div className="flex justify-end space-x-1">
                    <AlertDialog>
                      <AlertDialogTrigger asChild>
                        <Button
                          variant="ghost"
                          size="icon"
                          className="h-8 w-8 text-red-500 cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700"
                          disabled={deletingProductId === product.id}
                        >
                          {deletingProductId === product.id ? (
                            <LoaderCircle className="h-4 w-4 animate-spin" />
                          ) : (
                            <Trash className="h-4 w-4" />
                          )}
                          <span className="sr-only">Delete</span>
                        </Button>
                      </AlertDialogTrigger>
                      <AlertDialogContent>
                        <AlertDialogHeader>
                          <AlertDialogTitle>Konfirmasi Hapus</AlertDialogTitle>
                          <AlertDialogDescription>
                            Apakah Anda yakin ingin menghapus produk{" "}
                            {product.name}? Tindakan ini tidak dapat dibatalkan.
                          </AlertDialogDescription>
                        </AlertDialogHeader>
                        <AlertDialogFooter>
                          <AlertDialogCancel className="cursor-pointer">
                            Batal
                          </AlertDialogCancel>
                          <AlertDialogAction
                            onClick={() => handleDeleteProduct(product.id)}
                            disabled={deletingProductId === product.id}
                            className="cursor-pointer bg-red-500 hover:bg-red-600"
                          >
                            {deletingProductId === product.id ? (
                              <div className="flex items-center gap-2">
                                <LoaderCircle className="h-4 w-4 animate-spin" />
                                <span>Menghapus...</span>
                              </div>
                            ) : (
                              "Hapus"
                            )}
                          </AlertDialogAction>
                        </AlertDialogFooter>
                      </AlertDialogContent>
                    </AlertDialog>
                  </div>
                </td>
              </tr>
            ))
          ) : (
            <tr>
              <td
                colSpan={
                  Object.values(columnVisibility).filter(Boolean).length + 1
                }
                className="px-6 py-4 text-center text-gray-500 dark:text-gray-400"
              >
                {searchTerm
                  ? "Tidak ada produk yang sesuai dengan pencarian."
                  : "Belum ada data produk."}
              </td>
            </tr>
          )}
        </tbody>
      </table>
    </div>
  );
};
