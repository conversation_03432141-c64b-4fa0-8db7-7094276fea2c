"use server";

import { z } from "zod";
import { SaleSchema } from "@/schemas/zod";
import { db } from "@/lib/prisma";
import { revalidatePath } from "next/cache";
import { auth } from "@/lib/auth";
import { getEffectiveUserId } from "@/lib/get-effective-user-id";
import {
  createSaleSuccessNotification,
  createLowStockNotification,
} from "@/lib/create-system-notification";
import { generateSalesId } from "@/lib/generate-id";

// Function to get the next transaction number
export const getNextTransactionNumber = async (
  prefix: string,
  date?: Date | string
) => {
  try {
    // Use provided date or current date
    const baseDate = date ? new Date(date) : new Date();
    const year = baseDate.getFullYear();

    // Count sales for the current year to determine the next sequential number
    const saleCount = await db.sale.count({
      where: {
        transactionNumber: {
          startsWith: `${prefix.toUpperCase()}-${year}-`,
        },
      },
    });

    // Calculate the next number (add 1 to the current count)
    const nextNumber = saleCount + 1;

    // Format the number as a 6-digit string with leading zeros for TRX or 4-digit for INV
    if (prefix.toUpperCase() === "TRX") {
      // Format: TRX-2024-S000001
      const formattedNumber = `S${String(nextNumber).padStart(6, "0")}`;
      const result = `${prefix.toUpperCase()}-${year}-${formattedNumber}`;

      return {
        success: true,
        nextNumber: result,
      };
    } else {
      // Format: INV-2024-S0001 (for other prefixes)
      const formattedNumber = `S${String(nextNumber).padStart(6, "0")}`;
      const result = `${prefix.toUpperCase()}-${year}-${formattedNumber}`;

      return {
        success: true,
        nextNumber: result,
      };
    }
  } catch (error) {
    console.error("Error generating next transaction number:", error);

    // Get current year for fallback
    const fallbackDate = date ? new Date(date) : new Date();
    const year = fallbackDate.getFullYear();

    // Fallback based on prefix
    if (prefix.toUpperCase() === "TRX") {
      return {
        success: true,
        nextNumber: `${prefix.toUpperCase()}-${year}-S000001`,
      };
    } else {
      return {
        success: true,
        nextNumber: `${prefix.toUpperCase()}-${year}-S000001`,
      };
    }
  }
};

export const addSale = async (values: z.infer<typeof SaleSchema>) => {
  // Get effective user ID (owner ID if employee, user's own ID otherwise)
  const effectiveUserId = await getEffectiveUserId();

  if (!effectiveUserId) {
    return { error: "Tidak terautentikasi!" };
  }
  const userId = effectiveUserId;

  // 1. Validate input server-side
  const validatedFields = SaleSchema.safeParse(values);

  if (!validatedFields.success) {
    console.error(
      "Validation Error:",
      validatedFields.error.flatten().fieldErrors
    );
    return { error: "Input tidak valid!" };
  }

  const {
    items,
    totalAmount,
    transactionNumber: inputTransactionNumber,
    invoiceRef: inputInvoiceRef,
  } = validatedFields.data;

  try {
    // 2. Create sale in database with transaction to ensure all operations succeed or fail together
    const result = await db.$transaction(async (tx) => {
      // Generate a custom sales ID with company-specific sequence
      const customSalesId = await generateSalesId(userId);

      // Generate transaction number if not provided
      let transactionNumber = inputTransactionNumber;
      if (!transactionNumber || transactionNumber.trim() === "") {
        const trxResult = await getNextTransactionNumber("TRX");
        if (trxResult.success) {
          transactionNumber = trxResult.nextNumber;
        }
      }

      // Generate invoice number if not provided
      let invoiceRef = inputInvoiceRef;
      if (!invoiceRef || invoiceRef.trim() === "") {
        const invResult = await getNextTransactionNumber("INV");
        if (invResult.success) {
          invoiceRef = invResult.nextNumber;
        }
      }

      // Create the sale record with custom ID
      const sale = await tx.sale.create({
        data: {
          id: customSalesId, // Use the custom ID
          totalAmount,
          userId,
          transactionNumber, // Set the transaction number
          invoiceRef, // Set the invoice reference
          items: {
            create: items.map((item) => ({
              quantity: item.quantity,
              priceAtSale: item.priceAtSale,
              productId: item.productId,
            })),
          },
        },
        include: {
          items: true,
        },
      });

      // Update product stock for each item sold and check for low stock
      const lowStockThreshold = 5; // Define a threshold for low stock notifications
      const lowStockProducts = [];

      for (const item of items) {
        // Get the product to check current stock
        const product = await tx.product.findUnique({
          where: { id: item.productId },
          select: { id: true, name: true, stock: true },
        });

        if (!product) continue;

        // Calculate new stock level
        const newStock = product.stock - item.quantity;

        // Update the stock
        await tx.product.update({
          where: { id: item.productId },
          data: {
            stock: {
              decrement: item.quantity,
            },
          },
        });

        // Check if stock is below threshold
        if (newStock <= lowStockThreshold && newStock > 0) {
          lowStockProducts.push({
            id: product.id,
            name: product.name,
            stock: newStock,
          });
        }
      }

      return sale;
    });

    // 3. Create notifications
    // Sale success notification
    await createSaleSuccessNotification(
      result.id,
      result.totalAmount.toNumber()
    );

    // Low stock notifications
    const lowStockProducts = [];

    // Get updated stock levels for products in this sale
    for (const item of items) {
      const product = await db.product.findUnique({
        where: { id: item.productId },
        select: { id: true, name: true, stock: true },
      });

      if (product && product.stock <= 5 && product.stock > 0) {
        lowStockProducts.push(product);
      }
    }

    // Create low stock notifications
    for (const product of lowStockProducts) {
      await createLowStockNotification(product.name, product.stock);
    }

    // 4. Revalidate the sales page cache
    revalidatePath("/dashboard/sales");

    // Convert Decimal to number for serialization
    const serializedResult = {
      ...result,
      totalAmount: result.totalAmount.toNumber(),
      items: result.items.map((item) => ({
        ...item,
        priceAtSale: item.priceAtSale.toNumber(),
      })),
    };

    return {
      success: "Penjualan berhasil dicatat!",
      data: serializedResult,
    };
  } catch (error) {
    console.error("Database Error:", error);
    return { error: "Gagal mencatat penjualan. Silakan coba lagi." };
  }
};

export const getSales = async () => {
  // Get effective user ID (owner ID if employee, user's own ID otherwise)
  const effectiveUserId = await getEffectiveUserId();

  if (!effectiveUserId) {
    return { error: "Tidak terautentikasi!" };
  }
  const userId = effectiveUserId;

  try {
    const sales = await db.sale.findMany({
      where: {
        userId,
      },
      orderBy: {
        saleDate: "desc",
      },
      include: {
        items: {
          include: {
            product: {
              select: {
                name: true,
              },
            },
          },
        },
        user: {
          select: {
            id: true,
            name: true,
            username: true,
          },
        },
      },
    });

    // Convert Decimal to number for serialization
    const serializedSales = sales.map((sale) => ({
      ...sale,
      totalAmount: sale.totalAmount.toNumber(),
      items: sale.items.map((item) => ({
        ...item,
        priceAtSale: item.priceAtSale.toNumber(),
      })),
    }));

    return { sales: serializedSales };
  } catch (error) {
    console.error("Database Error:", error);
    return { error: "Gagal mengambil data penjualan." };
  }
};

export const getSaleById = async (id: string) => {
  // Get effective user ID (owner ID if employee, user's own ID otherwise)
  const effectiveUserId = await getEffectiveUserId();

  if (!effectiveUserId) {
    return { error: "Tidak terautentikasi!" };
  }
  const userId = effectiveUserId;

  try {
    // First try to find by transaction number (case-insensitive)
    let sale = await db.sale.findFirst({
      where: {
        transactionNumber: {
          mode: "insensitive",
          equals: id,
        },
        userId, // Ensure the sale belongs to the current user
      },
      include: {
        items: {
          include: {
            product: true,
          },
        },
        user: {
          select: {
            id: true,
            name: true,
            username: true,
          },
        },
      },
    });

    // If not found by transaction number, check if the ID starts with "sal-" (case-insensitive)
    if (!sale && id.toLowerCase().startsWith("sal-")) {
      sale = await db.sale.findFirst({
        where: {
          id: {
            mode: "insensitive",
            equals: id,
          },
          userId, // Ensure the sale belongs to the current user
        },
        include: {
          items: {
            include: {
              product: true,
            },
          },
          user: {
            select: {
              id: true,
              name: true,
              username: true,
            },
          },
        },
      });
    }

    // If still not found, try to find by exact ID
    if (!sale) {
      sale = await db.sale.findUnique({
        where: {
          id,
          userId, // Ensure the sale belongs to the current user
        },
        include: {
          items: {
            include: {
              product: true,
            },
          },
          user: {
            select: {
              id: true,
              name: true,
              username: true,
            },
          },
        },
      });
    }

    if (!sale) {
      return { error: "Penjualan tidak ditemukan." };
    }

    // Convert Decimal to number for serialization
    const serializedSale = {
      ...sale,
      totalAmount: sale.totalAmount.toNumber(),
      items: sale.items.map((item) => ({
        ...item,
        priceAtSale: item.priceAtSale.toNumber(),
        product: {
          ...item.product,
          price: item.product.price.toNumber(),
          cost: item.product.cost ? item.product.cost.toNumber() : null,
        },
      })),
    };

    return { sale: serializedSale };
  } catch (error) {
    console.error("Database Error:", error);
    return { error: "Gagal mengambil detail penjualan." };
  }
};

export const updateSale = async (
  id: string,
  values: z.infer<typeof SaleSchema>
) => {
  // Get effective user ID (owner ID if employee, user's own ID otherwise)
  const effectiveUserId = await getEffectiveUserId();

  if (!effectiveUserId) {
    return { error: "Tidak terautentikasi!" };
  }
  const userId = effectiveUserId;

  // 1. Validate input server-side
  const validatedFields = SaleSchema.safeParse(values);

  if (!validatedFields.success) {
    console.error(
      "Validation Error:",
      validatedFields.error.flatten().fieldErrors
    );
    return { error: "Input tidak valid!" };
  }

  const { items, totalAmount, invoiceRef, transactionNumber } =
    validatedFields.data;

  try {
    // First try to find by transaction number (case-insensitive)
    let existingSale = await db.sale.findFirst({
      where: {
        transactionNumber: {
          mode: "insensitive",
          equals: id,
        },
        userId, // Ensure the sale belongs to the current user
      },
      include: {
        items: true,
      },
    });

    // If not found by transaction number, check if the ID starts with "sal-" (case-insensitive)
    if (!existingSale && id.toLowerCase().startsWith("sal-")) {
      existingSale = await db.sale.findFirst({
        where: {
          id: {
            mode: "insensitive",
            equals: id,
          },
          userId, // Ensure the sale belongs to the current user
        },
        include: {
          items: true,
        },
      });
    }

    // If still not found, try to find by exact ID
    if (!existingSale) {
      existingSale = await db.sale.findUnique({
        where: {
          id,
          userId,
        },
        include: {
          items: true,
        },
      });
    }

    if (!existingSale) {
      return { error: "Penjualan tidak ditemukan!" };
    }

    // Get the original items to calculate stock adjustments
    const originalItems = existingSale.items;

    // 2. Update sale in database with transaction to ensure all operations succeed or fail together
    const result = await db.$transaction(async (tx) => {
      // First, delete all existing items
      await tx.saleItem.deleteMany({
        where: {
          saleId: id,
        },
      });

      // Update the sale record
      const sale = await tx.sale.update({
        where: {
          id,
        },
        data: {
          totalAmount,
          transactionNumber,
          invoiceRef,
          items: {
            create: items.map((item) => ({
              quantity: item.quantity,
              priceAtSale: item.priceAtSale,
              productId: item.productId,
            })),
          },
        },
        include: {
          items: true,
        },
      });

      // Adjust product stock: first add back the original quantities, then subtract the new quantities
      // This handles both new items, removed items, and quantity changes

      // Add back original quantities (reverse the original sale)
      for (const item of originalItems) {
        await tx.product.update({
          where: { id: item.productId },
          data: {
            stock: {
              increment: item.quantity,
            },
          },
        });
      }

      // Subtract new quantities (apply the updated sale)
      for (const item of items) {
        await tx.product.update({
          where: { id: item.productId },
          data: {
            stock: {
              decrement: item.quantity,
            },
          },
        });
      }

      return sale;
    });

    // 3. Revalidate the sales page cache
    revalidatePath("/dashboard/sales");
    revalidatePath(`/dashboard/sales/detail/${id}`);
    revalidatePath(`/dashboard/sales/edit/${id}`);

    // Also revalidate using transactionNumber if available
    if (existingSale.transactionNumber) {
      revalidatePath(
        `/dashboard/sales/detail/${existingSale.transactionNumber}`
      );
      revalidatePath(`/dashboard/sales/edit/${existingSale.transactionNumber}`);
    }

    // Convert Decimal to number for serialization
    const serializedResult = {
      ...result,
      totalAmount: result.totalAmount.toNumber(),
      items: result.items.map((item) => ({
        ...item,
        priceAtSale: item.priceAtSale.toNumber(),
      })),
    };

    return {
      success: "Penjualan berhasil diperbarui!",
      data: serializedResult,
    };
  } catch (error) {
    console.error("Database Error:", error);
    return { error: "Gagal memperbarui penjualan. Silakan coba lagi." };
  }
};

export const deleteSale = async (id: string) => {
  // Get effective user ID (owner ID if employee, user's own ID otherwise)
  const effectiveUserId = await getEffectiveUserId();

  if (!effectiveUserId) {
    return { error: "Tidak terautentikasi!" };
  }
  const userId = effectiveUserId;

  try {
    // First try to find by transaction number (case-insensitive)
    let existingSale = await db.sale.findFirst({
      where: {
        transactionNumber: {
          mode: "insensitive",
          equals: id,
        },
        userId, // Ensure the sale belongs to the current user
      },
      include: {
        items: true,
      },
    });

    // If not found by transaction number, check if the ID starts with "sal-" (case-insensitive)
    if (!existingSale && id.toLowerCase().startsWith("sal-")) {
      existingSale = await db.sale.findFirst({
        where: {
          id: {
            mode: "insensitive",
            equals: id,
          },
          userId, // Ensure the sale belongs to the current user
        },
        include: {
          items: true,
        },
      });
    }

    // If still not found, try to find by exact ID
    if (!existingSale) {
      existingSale = await db.sale.findUnique({
        where: {
          id,
          userId,
        },
        include: {
          items: true,
        },
      });
    }

    if (!existingSale) {
      return { error: "Penjualan tidak ditemukan!" };
    }

    // Get the original items to revert stock changes
    const originalItems = existingSale.items;

    // Use a transaction to ensure all operations succeed or fail together
    await db.$transaction(async (tx) => {
      // First, revert the stock changes by incrementing the stock for each item
      for (const item of originalItems) {
        await tx.product.update({
          where: { id: item.productId },
          data: {
            stock: {
              increment: item.quantity,
            },
          },
        });
      }

      // Delete all sale items
      await tx.saleItem.deleteMany({
        where: {
          saleId: id,
        },
      });

      // Delete the sale
      await tx.sale.delete({
        where: {
          id,
          userId, // Ensure the sale belongs to the current user
        },
      });
    });

    // Revalidate the sales page cache
    revalidatePath("/dashboard/sales");
    revalidatePath(`/dashboard/sales/detail/${id}`);
    revalidatePath(`/dashboard/sales/edit/${id}`);

    // Also revalidate using transactionNumber if available
    if (existingSale.transactionNumber) {
      revalidatePath(
        `/dashboard/sales/detail/${existingSale.transactionNumber}`
      );
      revalidatePath(`/dashboard/sales/edit/${existingSale.transactionNumber}`);
    }

    return { success: "Penjualan berhasil dihapus!" };
  } catch (error) {
    console.error("Database Error:", error);
    return { error: "Gagal menghapus penjualan. Silakan coba lagi." };
  }
};
