"use client";

import React from "react";
import { SummaryCardsSection, SummaryData } from "./SummaryCardsSection";
import { SalesBarChart } from "./SalesBarChart";
import { ProductDistributionBarChart } from "./ProductDistributionBarChart";
import { RecentActivity } from "./RecentActivity";
import { DateRangeFilter, DateRange } from "./DateRangeFilter";

export interface SalesChartDataPoint {
  name: string;
  total: number;
}

export interface ProductDistributionDataPoint {
  name: string;
  value: number;
}

export interface RecentTransactionItem {
  id: string;
  time: string;
  amount: string;
  status?: "success" | "pending" | "failed";
}

interface OverviewTabProps {
  summaryData: SummaryData;
  salesChartData: SalesChartDataPoint[];
  productDistData: ProductDistributionDataPoint[];
  recentTransData: RecentTransactionItem[];
  className?: string;
}

export function OverviewTab({
  summaryData,
  salesChartData,
  productDistData,
  recentTransData,
  className = "",
}: OverviewTabProps) {
  const [dateRange, setDateRange] = React.useState<DateRange>("30days");

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Date Range Filter */}
      <div className="flex justify-end">
        <DateRangeFilter
          selectedRange={dateRange}
          onRangeChange={setDateRange}
        />
      </div>

      {/* Summary Cards */}
      <SummaryCardsSection summaryData={summaryData} />

      {/* Charts Section */}
      <div className="grid grid-cols-1 gap-6 lg:grid-cols-7">
        {/* Sales Chart */}
        <div className="col-span-1 lg:col-span-4">
          <SalesBarChart data={salesChartData} />
        </div>

        {/* Product Distribution Chart */}
        <div className="col-span-1 lg:col-span-3">
          <ProductDistributionBarChart data={productDistData} />
        </div>
      </div>

      {/* Recent Activity */}
      <div className="mt-6">
        <RecentActivity transactions={recentTransData} />
      </div>
    </div>
  );
}
