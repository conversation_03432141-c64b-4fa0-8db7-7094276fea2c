"use server";

import { auth } from "@/lib/auth";
import { db } from "@/lib/prisma";
import { z } from "zod";
import bcrypt from "bcryptjs";
import { PasswordUpdateSchema, PasswordSetSchema } from "@/schemas/password";

// Function to check if user is Google OAuth user without password
export const checkUserPasswordStatus = async () => {
  try {
    const session = await auth();
    const userId = session?.user?.id;

    if (!userId) {
      return { error: "Tidak terautentikasi!" };
    }

    const user = await db.user.findUnique({
      where: { id: userId },
      select: {
        id: true,
        password: true,
        provider: true,
      },
    });

    if (!user) {
      return { error: "Pengguna tidak ditemukan!" };
    }

    const hasPassword = !!user.password;
    const isGoogleUser = user.provider === "google";
    const needsCurrentPassword = hasPassword && !isGoogleUser;

    return {
      success: true,
      hasPassword,
      isGoogleUser,
      needsCurrentPassword,
      provider: user.provider,
    };
  } catch (error) {
    console.error("Error checking user password status:", error);
    return { error: "Gagal memeriksa status password." };
  }
};

export const updatePassword = async (
  values: z.infer<typeof PasswordSetSchema>
) => {
  try {
    // Get current session
    const session = await auth();
    const userId = session?.user?.id;

    if (!userId) {
      return { error: "Tidak terautentikasi!" };
    }

    // Get user with password and provider info
    const user = await db.user.findUnique({
      where: {
        id: userId,
      },
      select: {
        id: true,
        password: true,
        provider: true,
      },
    });

    if (!user) {
      return { error: "Pengguna tidak ditemukan!" };
    }

    // Determine if user has existing password
    const hasExistingPassword = !!user.password;
    const isGoogleUser = user.provider === "google";

    // Validate input based on whether user has existing password
    let validatedFields;
    if (hasExistingPassword && !isGoogleUser) {
      // User has existing password, require current password
      validatedFields = PasswordUpdateSchema.safeParse(values);
      if (!validatedFields.success) {
        return { error: validatedFields.error.errors[0].message };
      }

      // Verify current password
      const isPasswordValid = await bcrypt.compare(
        validatedFields.data.currentPassword,
        user.password!
      );

      if (!isPasswordValid) {
        return { error: "Password saat ini tidak valid!" };
      }
    } else {
      // Google OAuth user or user without password, don't require current password
      validatedFields = PasswordSetSchema.safeParse(values);
      if (!validatedFields.success) {
        return { error: validatedFields.error.errors[0].message };
      }
    }

    // Hash new password
    const hashedPassword = await bcrypt.hash(
      validatedFields.data.newPassword,
      12
    );

    // Update user password
    await db.user.update({
      where: {
        id: userId,
      },
      data: {
        password: hashedPassword,
      },
    });

    const message = hasExistingPassword
      ? "Password berhasil diperbarui!"
      : "Password berhasil dibuat!";

    return { success: message };
  } catch (error) {
    console.error("Error updating password:", error);
    return { error: "Gagal memperbarui password." };
  }
};
