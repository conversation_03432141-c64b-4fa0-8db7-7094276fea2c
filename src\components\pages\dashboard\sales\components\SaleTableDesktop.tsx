import React, { useState } from "react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { toast } from "sonner";
import { Sale, ColumnVisibility } from "../types"; // Import from the types file
import { Trash } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Badge } from "@/components/ui/badge";
import { deleteSale } from "@/actions/entities/sales";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";

interface SaleTableDesktopProps {
  sales: Sale[];
  columnVisibility: ColumnVisibility;
  handleSort: (field: string) => void;
  getSortIcon: (field: string) => React.ReactNode;
  searchTerm: string;
  selectedSales?: string[];
  setSelectedSales?: React.Dispatch<React.SetStateAction<string[]>>;
}

export const SaleTableDesktop: React.FC<SaleTableDesktopProps> = ({
  sales,
  columnVisibility,
  handleSort,
  getSortIcon,
  searchTerm,
  selectedSales = [],
  setSelectedSales = () => {},
}) => {
  const router = useRouter();
  const [saleToDelete, setSaleToDelete] = useState<string | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);

  // Handle delete sale
  const handleDeleteSale = async (id: string) => {
    setIsDeleting(true);
    try {
      const result = await deleteSale(id);
      if (result.success) {
        toast.success(result.success);
        // Refresh the page to show updated data
        router.refresh();
      } else if (result.error) {
        toast.error(result.error);
      }
    } catch (error) {
      console.error("Error deleting sale:", error);
      toast.error("Terjadi kesalahan saat menghapus penjualan.");
    } finally {
      setIsDeleting(false);
      setSaleToDelete(null);
    }
  };

  const formatDate = (date: Date | string) => {
    return new Date(date).toLocaleDateString("id-ID", {
      day: "2-digit",
      month: "2-digit",
      year: "numeric",
    });
  };

  // Handle select all sales
  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedSales(sales.map((sale) => sale.id));
    } else {
      setSelectedSales([]);
    }
  };

  // Handle select individual sale
  const handleSelectSale = (id: string, checked: boolean) => {
    if (checked) {
      setSelectedSales([...selectedSales, id]);
    } else {
      setSelectedSales(selectedSales.filter((saleId) => saleId !== id));
    }
  };

  return (
    <div className="relative overflow-x-auto border border-gray-200 dark:border-gray-700 rounded-lg">
      <table className="w-full text-sm text-left text-gray-500 dark:text-gray-400">
        <thead className="text-xs text-gray-700 dark:text-gray-300 uppercase bg-gray-50 dark:bg-gray-700">
          <tr>
            <th scope="col" className="px-4 py-3">
              <Checkbox
                checked={
                  sales.length > 0 && selectedSales.length === sales.length
                }
                onCheckedChange={handleSelectAll}
                aria-label="Select all"
              />
            </th>
            {columnVisibility.id && (
              <th
                scope="col"
                className="px-6 py-3 cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-600"
                onClick={() => handleSort("id")}
              >
                <div className="flex items-center">
                  No. Transaksi
                  {getSortIcon("id")}
                </div>
              </th>
            )}
            {columnVisibility.date && (
              <th
                scope="col"
                className="px-6 py-3 cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-600"
                onClick={() => handleSort("saleDate")}
              >
                <div className="flex items-center">
                  Tanggal
                  {getSortIcon("saleDate")}
                </div>
              </th>
            )}
            {columnVisibility.paymentDueDate && (
              <th
                scope="col"
                className="px-6 py-3 cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-600"
                onClick={() => handleSort("paymentDueDate")}
              >
                <div className="flex items-center">
                  Tgl. Jatuh Tempo
                  {getSortIcon("paymentDueDate")}
                </div>
              </th>
            )}
            {columnVisibility.customer && (
              <th
                scope="col"
                className="px-6 py-3 cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-600"
                onClick={() => handleSort("customer")}
              >
                <div className="flex items-center">
                  Pelanggan
                  {getSortIcon("customer")}
                </div>
              </th>
            )}
            {columnVisibility.totalAmount && (
              <th
                scope="col"
                className="px-6 py-3 cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-600"
                onClick={() => handleSort("totalAmount")}
              >
                <div className="flex items-center">
                  Total
                  {getSortIcon("totalAmount")}
                </div>
              </th>
            )}
            {columnVisibility.itemCount && (
              <th
                scope="col"
                className="px-6 py-3 cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-600"
                onClick={() => handleSort("itemCount")}
              >
                <div className="flex items-center">
                  Jumlah Item
                  {getSortIcon("itemCount")}
                </div>
              </th>
            )}
            {columnVisibility.invoiceRef && (
              <th
                scope="col"
                className="px-6 py-3 cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-600"
                onClick={() => handleSort("invoiceRef")}
              >
                <div className="flex items-center">
                  No. Faktur
                  {getSortIcon("invoiceRef")}
                </div>
              </th>
            )}
            {columnVisibility.tags && (
              <th
                scope="col"
                className="px-6 py-3 cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-600"
                onClick={() => handleSort("tags")}
              >
                <div className="flex items-center">
                  Tag
                  {getSortIcon("tags")}
                </div>
              </th>
            )}
            <th scope="col" className="px-6 py-3 text-right">
              <span className="sr-only">Aksi</span>
            </th>
          </tr>
        </thead>
        <tbody>
          {sales.length > 0 ? (
            sales.map((sale) => (
              <tr
                key={sale.id}
                className="bg-white dark:bg-gray-800 border-b dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700"
              >
                <td className="px-4 py-4">
                  <Checkbox
                    checked={selectedSales.includes(sale.id)}
                    onCheckedChange={(checked) =>
                      handleSelectSale(sale.id, checked as boolean)
                    }
                    aria-label={`Select sale ${sale.id}`}
                  />
                </td>
                {columnVisibility.id && (
                  <td className="px-6 py-4 font-medium text-gray-900 dark:text-gray-100 whitespace-nowrap">
                    <Link
                      href={`/dashboard/sales/detail/${sale.transactionNumber || sale.id}`}
                      className="hover:underline text-blue-600 dark:text-blue-400 cursor-pointer"
                    >
                      {sale.transactionNumber ||
                        `${sale.id.substring(0, 4)}-${sale.id.substring(4, 8)}-${sale.id.substring(8, 12)}`}
                    </Link>
                  </td>
                )}
                {columnVisibility.date && (
                  <td className="px-6 py-4 whitespace-nowrap text-gray-500 dark:text-gray-400">
                    {formatDate(sale.saleDate)}
                  </td>
                )}
                {columnVisibility.paymentDueDate && (
                  <td className="px-6 py-4 whitespace-nowrap text-gray-500 dark:text-gray-400">
                    {sale.paymentDueDate
                      ? formatDate(sale.paymentDueDate)
                      : "-"}
                  </td>
                )}
                {columnVisibility.customer && (
                  <td className="px-6 py-4 whitespace-nowrap text-gray-500 dark:text-gray-400">
                    {sale.customer?.name || "Umum"}
                  </td>
                )}
                {columnVisibility.totalAmount && (
                  <td className="px-6 py-4 whitespace-nowrap text-gray-500 dark:text-gray-400">
                    Rp {sale.totalAmount.toLocaleString("id-ID")}
                  </td>
                )}
                {columnVisibility.itemCount && (
                  <td className="px-6 py-4 whitespace-nowrap text-gray-500 dark:text-gray-400">
                    {sale.items.length} item
                  </td>
                )}
                {columnVisibility.invoiceRef && (
                  <td className="px-6 py-4 whitespace-nowrap text-gray-500 dark:text-gray-400">
                    {sale.invoiceRef || "-"}
                  </td>
                )}
                {columnVisibility.tags && (
                  <td className="px-6 py-4 whitespace-nowrap text-gray-500 dark:text-gray-400">
                    <div className="flex flex-wrap gap-1">
                      {sale.tags && sale.tags.length > 0 ? (
                        sale.tags.map((tag, index) => (
                          <Badge
                            key={index}
                            variant="secondary"
                            className="bg-teal-100 text-teal-800 dark:bg-teal-900/30 dark:text-teal-300 shadow-sm"
                          >
                            {tag}
                          </Badge>
                        ))
                      ) : (
                        <span>-</span>
                      )}
                    </div>
                  </td>
                )}
                <td className="px-6 py-4 text-right whitespace-nowrap">
                  <div className="flex justify-end">
                    <AlertDialog>
                      <AlertDialogTrigger asChild>
                        <Button
                          variant="ghost"
                          size="icon"
                          className="h-8 w-8 text-red-500 cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700"
                        >
                          <Trash className="h-4 w-4" />
                          <span className="sr-only">Delete</span>
                        </Button>
                      </AlertDialogTrigger>
                      <AlertDialogContent>
                        <AlertDialogHeader>
                          <AlertDialogTitle>Konfirmasi Hapus</AlertDialogTitle>
                          <AlertDialogDescription>
                            Apakah Anda yakin ingin menghapus penjualan ini?
                            Tindakan ini tidak dapat dibatalkan dan akan
                            mengembalikan stok produk.
                          </AlertDialogDescription>
                        </AlertDialogHeader>
                        <AlertDialogFooter>
                          <AlertDialogCancel>Batal</AlertDialogCancel>
                          <AlertDialogAction
                            onClick={() => {
                              setSaleToDelete(sale.id);
                              handleDeleteSale(sale.id);
                            }}
                            disabled={isDeleting && saleToDelete === sale.id}
                            className="bg-red-500 hover:bg-red-600"
                          >
                            {isDeleting && saleToDelete === sale.id
                              ? "Menghapus..."
                              : "Hapus"}
                          </AlertDialogAction>
                        </AlertDialogFooter>
                      </AlertDialogContent>
                    </AlertDialog>
                  </div>
                </td>
              </tr>
            ))
          ) : (
            <tr>
              <td
                colSpan={
                  Object.values(columnVisibility).filter(Boolean).length + 2
                }
                className="px-6 py-4 text-center text-gray-500 dark:text-gray-400"
              >
                {searchTerm
                  ? "Tidak ada transaksi yang sesuai dengan pencarian."
                  : "Belum ada data penjualan."}
              </td>
            </tr>
          )}
        </tbody>
      </table>
    </div>
  );
};
