import React, { useState } from "react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { toast } from "sonner";
import { Purchase, ColumnVisibility } from "../types"; // Import from the types file
import { Trash } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { deletePurchase } from "@/actions/entities/purchases";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";

interface PurchaseTableDesktopProps {
  purchases: Purchase[];
  columnVisibility: ColumnVisibility;
  handleSort: (field: string) => void;
  getSortIcon: (field: string) => React.ReactNode;
  searchTerm: string;
  onSelectionChange?: (selectedIds: string[]) => void;
}

export const PurchaseTableDesktop: React.FC<PurchaseTableDesktopProps> = ({
  purchases,
  columnVisibility,
  handleSort,
  getSortIcon,
  searchTerm,
  onSelectionChange,
}) => {
  const router = useRouter();
  const [purchaseToDelete, setPurchaseToDelete] = useState<string | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);
  const [selectedPurchases, setSelectedPurchases] = useState<string[]>([]);
  const [selectAll, setSelectAll] = useState(false);

  // Handle select all checkbox
  const handleSelectAll = (checked: boolean) => {
    setSelectAll(checked);
    if (checked) {
      const allIds = purchases.map((purchase) => purchase.id);
      setSelectedPurchases(allIds);
      if (onSelectionChange) onSelectionChange(allIds);
    } else {
      setSelectedPurchases([]);
      if (onSelectionChange) onSelectionChange([]);
    }
  };

  // Handle individual checkbox selection
  const handleSelectPurchase = (purchaseId: string, checked: boolean) => {
    let newSelected: string[];

    if (checked) {
      newSelected = [...selectedPurchases, purchaseId];
    } else {
      newSelected = selectedPurchases.filter((id) => id !== purchaseId);
      // If we're deselecting an item, also uncheck the "select all" checkbox
      if (selectAll) setSelectAll(false);
    }

    setSelectedPurchases(newSelected);
    if (onSelectionChange) onSelectionChange(newSelected);
  };

  // Handle delete purchase
  const handleDeletePurchase = async (id: string) => {
    setIsDeleting(true);
    try {
      const result = await deletePurchase(id);
      if (result.success) {
        toast.success(result.success);
        // Refresh the page to show updated data
        router.refresh();
      } else if (result.error) {
        toast.error(result.error);
      }
    } catch (error) {
      console.error("Error deleting purchase:", error);
      toast.error("Terjadi kesalahan saat menghapus pembelian.");
    } finally {
      setIsDeleting(false);
      setPurchaseToDelete(null);
    }
  };

  // Format date for display in concise format (DD/MM/YYYY)
  const formatDate = (date: Date | string) => {
    const d = new Date(date);
    const day = String(d.getDate()).padStart(2, "0");
    const month = String(d.getMonth() + 1).padStart(2, "0"); // Month is 0-indexed
    const year = d.getFullYear();

    return `${day}/${month}/${year}`;
  };
  return (
    <div className="relative overflow-x-auto border border-gray-200 dark:border-gray-700 rounded-lg">
      <table className="w-full text-sm text-left text-gray-500 dark:text-gray-400">
        <thead className="text-xs text-gray-700 dark:text-gray-300 uppercase bg-gray-50 dark:bg-gray-700">
          <tr>
            {/* Checkbox column - always visible */}
            <th scope="col" className="px-4 py-3">
              <Checkbox
                checked={selectAll}
                onCheckedChange={handleSelectAll}
                aria-label="Select all purchases"
              />
            </th>
            {columnVisibility.id && (
              <th
                scope="col"
                className="px-6 py-3 cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-600"
                onClick={() => handleSort("id")}
              >
                <div className="flex items-center">
                  No. Transaksi
                  {getSortIcon("id")}
                </div>
              </th>
            )}
            {columnVisibility.date && (
              <th
                scope="col"
                className="px-6 py-3 cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-600"
                onClick={() => handleSort("purchaseDate")}
              >
                <div className="flex items-center">
                  Tanggal
                  {getSortIcon("purchaseDate")}
                </div>
              </th>
            )}
            {columnVisibility.paymentDueDate && (
              <th
                scope="col"
                className="px-6 py-3 cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-600"
                onClick={() => handleSort("paymentDueDate")}
              >
                <div className="flex items-center">
                  Tgl. Jatuh Tempo
                  {getSortIcon("paymentDueDate")}
                </div>
              </th>
            )}
            {columnVisibility.supplier && (
              <th
                scope="col"
                className="px-6 py-3 cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-600"
                onClick={() => handleSort("supplier")}
              >
                <div className="flex items-center">
                  Supplier
                  {getSortIcon("supplier")}
                </div>
              </th>
            )}
            {columnVisibility.totalAmount && (
              <th
                scope="col"
                className="px-6 py-3 cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-600"
                onClick={() => handleSort("totalAmount")}
              >
                <div className="flex items-center">
                  Total
                  {getSortIcon("totalAmount")}
                </div>
              </th>
            )}
            {columnVisibility.itemCount && (
              <th
                scope="col"
                className="px-6 py-3 cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-600"
                onClick={() => handleSort("itemCount")}
              >
                <div className="flex items-center">
                  Jumlah Item
                  {getSortIcon("itemCount")}
                </div>
              </th>
            )}
            {columnVisibility.invoiceRef && (
              <th
                scope="col"
                className="px-6 py-3 cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-600"
                onClick={() => handleSort("invoiceRef")}
              >
                <div className="flex items-center">
                  No. Faktur
                  {getSortIcon("invoiceRef")}
                </div>
              </th>
            )}
            {columnVisibility.tags && (
              <th
                scope="col"
                className="px-6 py-3 cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-600"
                onClick={() => handleSort("tags")}
              >
                <div className="flex items-center">
                  Tag
                  {getSortIcon("tags")}
                </div>
              </th>
            )}
            <th scope="col" className="px-6 py-3 text-right">
              <span className="sr-only">Aksi</span>
            </th>
          </tr>
        </thead>
        <tbody>
          {purchases.length > 0 ? (
            purchases.map((purchase) => (
              <tr
                key={purchase.id}
                className="bg-white dark:bg-gray-800 border-b dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700"
              >
                {/* Checkbox cell - always visible */}
                <td className="px-4 py-4">
                  <Checkbox
                    checked={selectedPurchases.includes(purchase.id)}
                    onCheckedChange={(checked) =>
                      handleSelectPurchase(purchase.id, checked === true)
                    }
                    aria-label={`Select purchase ${purchase.id}`}
                  />
                </td>
                {columnVisibility.id && (
                  <td className="px-6 py-4 font-medium text-gray-900 dark:text-gray-100 whitespace-nowrap">
                    <Link
                      href={`/dashboard/purchases/detail/${purchase.transactionNumber || purchase.id}`}
                      className="hover:underline text-blue-600 dark:text-blue-400 cursor-pointer"
                    >
                      {purchase.transactionNumber ||
                        purchase.id.substring(0, 8)}
                    </Link>
                  </td>
                )}
                {columnVisibility.date && (
                  <td className="px-6 py-4 whitespace-nowrap text-gray-500 dark:text-gray-400">
                    {formatDate(purchase.purchaseDate)}
                  </td>
                )}
                {columnVisibility.paymentDueDate && (
                  <td className="px-6 py-4 whitespace-nowrap text-gray-500 dark:text-gray-400">
                    {purchase.paymentDueDate
                      ? formatDate(purchase.paymentDueDate)
                      : "-"}
                  </td>
                )}
                {columnVisibility.supplier && (
                  <td className="px-6 py-4 whitespace-nowrap text-gray-500 dark:text-gray-400">
                    {purchase.supplier?.name || "-"}
                  </td>
                )}
                {columnVisibility.totalAmount && (
                  <td className="px-6 py-4 whitespace-nowrap text-gray-500 dark:text-gray-400">
                    Rp {purchase.totalAmount.toLocaleString("id-ID")}
                  </td>
                )}
                {columnVisibility.itemCount && (
                  <td className="px-6 py-4 whitespace-nowrap text-gray-500 dark:text-gray-400">
                    {purchase.items.length}
                  </td>
                )}
                {columnVisibility.invoiceRef && (
                  <td className="px-6 py-4 whitespace-nowrap text-gray-500 dark:text-gray-400">
                    {purchase.invoiceRef || "-"}
                  </td>
                )}
                {columnVisibility.tags && (
                  <td className="px-6 py-4 whitespace-nowrap text-gray-500 dark:text-gray-400">
                    {purchase.tags && purchase.tags.length > 0
                      ? purchase.tags.join(", ")
                      : "-"}
                  </td>
                )}
                <td className="px-6 py-4 text-right whitespace-nowrap">
                  <div className="flex justify-end">
                    <AlertDialog>
                      <AlertDialogTrigger asChild>
                        <Button
                          variant="ghost"
                          size="icon"
                          className="h-8 w-8 text-red-500 cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700"
                        >
                          <Trash className="h-4 w-4" />
                          <span className="sr-only">Delete</span>
                        </Button>
                      </AlertDialogTrigger>
                      <AlertDialogContent>
                        <AlertDialogHeader>
                          <AlertDialogTitle>Konfirmasi Hapus</AlertDialogTitle>
                          <AlertDialogDescription>
                            Apakah Anda yakin ingin menghapus pembelian ini?
                            Tindakan ini tidak dapat dibatalkan dan akan
                            mengurangi stok produk.
                          </AlertDialogDescription>
                        </AlertDialogHeader>
                        <AlertDialogFooter>
                          <AlertDialogCancel>Batal</AlertDialogCancel>
                          <AlertDialogAction
                            onClick={() => {
                              setPurchaseToDelete(purchase.id);
                              handleDeletePurchase(purchase.id);
                            }}
                            disabled={
                              isDeleting && purchaseToDelete === purchase.id
                            }
                            className="bg-red-500 hover:bg-red-600"
                          >
                            {isDeleting && purchaseToDelete === purchase.id
                              ? "Menghapus..."
                              : "Hapus"}
                          </AlertDialogAction>
                        </AlertDialogFooter>
                      </AlertDialogContent>
                    </AlertDialog>
                  </div>
                </td>
              </tr>
            ))
          ) : (
            <tr>
              <td
                colSpan={
                  Object.values(columnVisibility).filter(Boolean).length + 2 // +2 for checkbox and actions columns
                }
                className="px-6 py-4 text-center text-gray-500 dark:text-gray-400"
              >
                {searchTerm
                  ? "Tidak ada pembelian yang sesuai dengan pencarian."
                  : "Belum ada data pembelian."}
              </td>
            </tr>
          )}
        </tbody>
      </table>
    </div>
  );
};
