import React from "react";
import DashboardLayout from "@/components/layout/dashboardlayout";
import SuppliersPage from "@/components/pages/dashboard/suppliers/suppliers";
import { auth } from "@/lib/auth";
import { getSuppliers } from "@/actions/entities/suppliers";
import { redirect } from "next/navigation";
import { Metadata } from "next";

export const metadata: Metadata = {
  title: "Supplier | Kasir Online",
  description: "Kelola data supplier Anda",
};

const Suppliers = async () => {
  const session = await auth();
  if (!session?.user?.id) {
    redirect("/login");
  }

  // Fetch suppliers for the current user
  const { suppliers, error } = await getSuppliers();

  if (error) {
    console.error("Error fetching suppliers:", error);
    return <p>Error: {error}</p>;
  }

  return (
    <DashboardLayout>
      {/* Pass the fetched suppliers to the client component */}
      <SuppliersPage suppliers={suppliers || []} />
    </DashboardLayout>
  );
};

export default Suppliers;
