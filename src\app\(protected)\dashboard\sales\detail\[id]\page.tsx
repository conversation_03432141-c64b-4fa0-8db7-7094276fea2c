import React from "react";
import { notFound } from "next/navigation";
import SaleDetailPage from "@/components/pages/dashboard/sales/detail";
import { getSaleById } from "@/actions/entities/sales";
import { Metadata } from "next";

export const metadata: Metadata = {
  title: "Detail Penjualan - Kasir Online",
  description: "Lihat detail transaksi penjualan",
};

type PageProps = {
  params: Promise<{ id: string }>;
  searchParams?: Promise<Record<string, string | string[]>>;
};

export default async function SaleDetail(props: PageProps) {
  // Get the id from params (which is now a Promise)
  const params = await props.params;
  const id = params.id;

  // Fetch the sale with the given ID
  const saleResult = await getSaleById(id);

  // If sale not found, return 404
  if (!saleResult.sale) {
    notFound();
  }

  // The sale data is already serialized in getSaleById
  // Convert Date objects to strings for the component
  const serializedSale = {
    ...saleResult.sale,
    saleDate: saleResult.sale.saleDate.toISOString(),
    createdAt: saleResult.sale.createdAt.toISOString(),
    updatedAt: saleResult.sale.updatedAt.toISOString(),
    items: saleResult.sale.items.map((item) => ({
      ...item,
      createdAt: item.createdAt.toISOString(),
      updatedAt: item.updatedAt.toISOString(),
    })),
  };

  return <SaleDetailPage sale={serializedSale} />;
}
