"use server";

import { db } from "@/lib/prisma";
import { auth } from "@/lib/auth";
import { formatDistanceToNowStrict } from "date-fns";
import { id } from "date-fns/locale";

export type UserActivityType = "login" | "profile_update" | "password_change" | "session_revoked";

export interface UserActivity {
  id: string;
  type: UserActivityType;
  description: string;
  timestamp: string; // Formatted time ago (e.g., "10 menit lalu")
  device: string | null;
  date: Date;
}

/**
 * Get recent user activities
 */
export async function getUserActivities(limit: number = 5): Promise<{
  success: boolean;
  data?: UserActivity[];
  error?: string;
}> {
  try {
    const session = await auth();
    
    if (!session?.user?.id) {
      return { success: false, error: "Tidak terautentikasi" };
    }
    
    // Get sessions for device information
    const sessions = await db.session.findMany({
      where: {
        userId: session.user.id,
      },
      orderBy: {
        expires: 'desc',
      },
      take: 10, // Get more than we need for matching
    });
    
    // Get user's login history from the database
    // In a real app, you would have a dedicated UserActivity table
    // For this demo, we'll use the session data and some mock data
    
    // Get profile updates from the database
    const profileUpdates = await db.user.findUnique({
      where: {
        id: session.user.id,
      },
      select: {
        updatedAt: true,
      },
    });
    
    // Create activities array
    const activities: UserActivity[] = [];
    
    // Add login activities from sessions
    // In a real app, you would store user agent info in the session
    sessions.forEach((dbSession, index) => {
      // Skip the first session if it's the current one
      if (index === 0) {
        // Add the current session as a login activity
        activities.push({
          id: `login-${dbSession.id}`,
          type: "login",
          description: "Login berhasil",
          timestamp: formatDistanceToNowStrict(
            new Date(dbSession.expires.getTime() - 6 * 60 * 60 * 1000), // Assuming 6-hour sessions
            { addSuffix: true, locale: id }
          ),
          device: getDeviceInfo(dbSession.sessionToken),
          date: new Date(dbSession.expires.getTime() - 6 * 60 * 60 * 1000),
        });
      } else {
        // Add previous sessions
        activities.push({
          id: `login-${dbSession.id}`,
          type: "login",
          description: "Login berhasil",
          timestamp: formatDistanceToNowStrict(
            new Date(dbSession.expires.getTime() - 6 * 60 * 60 * 1000),
            { addSuffix: true, locale: id }
          ),
          device: getDeviceInfo(dbSession.sessionToken),
          date: new Date(dbSession.expires.getTime() - 6 * 60 * 60 * 1000),
        });
      }
    });
    
    // Add profile update activity if available
    if (profileUpdates?.updatedAt) {
      activities.push({
        id: `profile-update-${Date.now()}`,
        type: "profile_update",
        description: "Profil diperbarui",
        timestamp: formatDistanceToNowStrict(profileUpdates.updatedAt, {
          addSuffix: true,
          locale: id,
        }),
        device: activities[0]?.device || "Browser",
        date: profileUpdates.updatedAt,
      });
    }
    
    // Sort activities by date (newest first)
    activities.sort((a, b) => b.date.getTime() - a.date.getTime());
    
    // Return the limited number of activities
    return {
      success: true,
      data: activities.slice(0, limit),
    };
  } catch (error) {
    console.error("Error fetching user activities:", error);
    return { success: false, error: "Terjadi kesalahan saat mengambil aktivitas pengguna" };
  }
}

/**
 * Helper function to extract device info from session token
 * In a real app, you would store user agent info in the session
 */
function getDeviceInfo(sessionToken: string): string {
  // This is a mock implementation
  // In a real app, you would store user agent info in the session
  
  const devices = [
    "Chrome di Windows",
    "Firefox di Windows",
    "Safari di macOS",
    "Chrome di Android",
    "Safari di iPhone",
    "Edge di Windows",
  ];
  
  // Use the session token to deterministically select a device
  const hash = sessionToken.split("").reduce((acc, char) => {
    return acc + char.charCodeAt(0);
  }, 0);
  
  return devices[hash % devices.length];
}
