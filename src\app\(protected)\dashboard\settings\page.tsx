import React from "react";
import { auth } from "@/lib/auth";
import { redirect } from "next/navigation";
import DashboardLayout from "@/components/layout/dashboardlayout";
import SettingsLayout from "@/components/pages/dashboard/settings/settings-layout";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
} from "@/components/ui/card";

export default async function SettingsPage() {
  const session = await auth();
  if (!session?.user?.id) {
    redirect("/login");
  }

  return (
    <DashboardLayout>
      <SettingsLayout>
        <div className="p-6">
          <h2 className="text-2xl font-bold mb-4">
            Selamat Datang di Pengaturan
          </h2>
          <p className="text-muted-foreground mb-6">
            Pilih salah satu kategori di menu sebelah kiri untuk mengelola
            pengaturan Anda.
          </p>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle>Akun & Profil</CardTitle>
                <CardDescription>
                  Kelola informasi pribadi dan preferensi akun Anda
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground">
                  Perbarui foto profil, nama, email, dan informasi kontak
                  lainnya.
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Tampilan</CardTitle>
                <CardDescription>
                  Sesuaikan tampilan aplikasi sesuai preferensi Anda
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground">
                  Ubah tema, warna, dan preferensi tampilan lainnya.
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Keamanan</CardTitle>
                <CardDescription>
                  Kelola pengaturan keamanan dan autentikasi akun
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground">
                  Ubah password, aktifkan autentikasi dua faktor, dan kelola
                  sesi aktif.
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Langganan</CardTitle>
                <CardDescription>
                  Kelola langganan dan metode pembayaran Anda
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground">
                  Lihat status langganan, upgrade paket, dan kelola metode
                  pembayaran.
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </SettingsLayout>
    </DashboardLayout>
  );
}
