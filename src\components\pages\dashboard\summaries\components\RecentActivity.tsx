"use client";

import React from "react";
import {
  <PERSON>,
  Card<PERSON>ontent,
  CardDescription,
  <PERSON><PERSON><PERSON><PERSON>,
  Card<PERSON>eader,
  Card<PERSON>itle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import Link from "next/link";
import { ClockIcon } from "@heroicons/react/24/outline";

export interface RecentTransactionItem {
  id: string;
  time: string;
  amount: string;
  status?: "success" | "pending" | "failed";
}

interface RecentActivityProps {
  transactions: RecentTransactionItem[];
  className?: string;
}

export function RecentActivity({
  transactions,
  className = "",
}: RecentActivityProps) {
  return (
    <Card className={`border-none shadow-md dark:bg-gray-800 ${className}`}>
      <CardHeader>
        <CardTitle>Aktivitas Terbaru</CardTitle>
        <CardDescription>Transaksi terbaru di sistem</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="rounded-md border dark:border-gray-700">
          <div className="grid grid-cols-3 bg-muted/50 p-3 text-xs font-medium">
            <div>Transaksi ID</div>
            <div>Waktu</div>
            <div className="text-right">Total</div>
          </div>
          <div className="divide-y dark:divide-gray-700">
            {transactions.length > 0 ? (
              transactions.map((transaction) => (
                <div
                  key={transaction.id}
                  className="grid grid-cols-3 p-3 text-sm"
                >
                  <div className="truncate font-medium">{transaction.id}</div>
                  <div className="flex items-center text-gray-500">
                    <ClockIcon className="mr-1 h-3 w-3" />
                    {transaction.time}
                  </div>
                  <div className="text-right font-medium">
                    {transaction.amount}
                  </div>
                </div>
              ))
            ) : (
              <div className="p-3 text-sm text-gray-500 text-center">
                Tidak ada transaksi terbaru
              </div>
            )}
          </div>
        </div>
      </CardContent>
      <CardFooter>
        <Button asChild variant="outline" size="sm" className="w-full">
          <Link href="/dashboard/sales">Lihat Semua Transaksi</Link>
        </Button>
      </CardFooter>
    </Card>
  );
}
