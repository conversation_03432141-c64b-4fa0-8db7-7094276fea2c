"use client";

import React from "react";
import Link from "next/link";
import { Supplier as PrismaSupplier } from "@prisma/client";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  ArrowLeft,
  Edit,
  Building2,
  Mail,
  Phone,
  MapPin,
  Calendar,
  FileText,
  User,
  PhoneCall,
  IdCard,
  Banknote,
  Home,
  Truck,
  CreditCard,
  UserCheck,
} from "lucide-react";
import { format } from "date-fns";
import { id } from "date-fns/locale";

interface SupplierDetailPageProps {
  supplier: PrismaSupplier;
}

const SupplierDetailPage: React.FC<SupplierDetailPageProps> = ({
  supplier,
}) => {
  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Link href="/dashboard/contacts?tab=supplier">
              <Button
                variant="ghost"
                size="sm"
                className="flex items-center gap-2"
              >
                <ArrowLeft className="h-4 w-4" />
                Kembali
              </Button>
            </Link>
            <div>
              <h1 className="text-2xl font-bold text-gray-900 dark:text-white flex items-center gap-3">
                <Building2 className="h-6 w-6 text-green-600 dark:text-green-400" />
                Detail Supplier
              </h1>
              <p className="text-gray-600 dark:text-gray-400 mt-1">
                Informasi lengkap supplier
              </p>
            </div>
          </div>
          <Link href={`/dashboard/suppliers/edit/${supplier.id}`}>
            <Button className="flex items-center gap-2">
              <Edit className="h-4 w-4" />
              Edit
            </Button>
          </Link>
        </div>
      </div>

      {/* Supplier Information */}
      <div className="max-w-4xl mx-auto px-6">
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden">
          {/* Supplier Header */}
          <div className="bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <div className="flex items-center gap-4">
              <div className="w-12 h-12 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center">
                <Building2 className="h-6 w-6 text-green-600 dark:text-green-400" />
              </div>
              <div>
                <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
                  {supplier.name}
                </h2>
                {supplier.contactName && (
                  <p className="text-gray-600 dark:text-gray-400">
                    Kontak: {supplier.contactName}
                  </p>
                )}
              </div>
            </div>
          </div>

          {/* Supplier Details */}
          <div className="p-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* Personal Information */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium text-gray-900 dark:text-white border-b border-gray-200 dark:border-gray-700 pb-2">
                  Informasi Personal
                </h3>

                <div className="space-y-3">
                  {supplier.firstName && (
                    <div className="flex items-center gap-3">
                      <User className="h-4 w-4 text-gray-400" />
                      <span className="text-sm text-gray-600 dark:text-gray-400 w-24">
                        Nama Depan:
                      </span>
                      <span className="text-sm text-gray-900 dark:text-white">
                        {supplier.firstName}
                      </span>
                    </div>
                  )}

                  {supplier.middleName && (
                    <div className="flex items-center gap-3">
                      <User className="h-4 w-4 text-gray-400" />
                      <span className="text-sm text-gray-600 dark:text-gray-400 w-24">
                        Nama Tengah:
                      </span>
                      <span className="text-sm text-gray-900 dark:text-white">
                        {supplier.middleName}
                      </span>
                    </div>
                  )}

                  {supplier.lastName && (
                    <div className="flex items-center gap-3">
                      <User className="h-4 w-4 text-gray-400" />
                      <span className="text-sm text-gray-600 dark:text-gray-400 w-24">
                        Nama Belakang:
                      </span>
                      <span className="text-sm text-gray-900 dark:text-white">
                        {supplier.lastName}
                      </span>
                    </div>
                  )}

                  <div className="flex items-center gap-3">
                    <User className="h-4 w-4 text-gray-400" />
                    <span className="text-sm text-gray-600 dark:text-gray-400 w-24">
                      Nama Kontak:
                    </span>
                    <span className="text-sm text-gray-900 dark:text-white">
                      {supplier.contactName || "-"}
                    </span>
                  </div>

                  {supplier.companyName && (
                    <div className="flex items-center gap-3">
                      <Building2 className="h-4 w-4 text-gray-400" />
                      <span className="text-sm text-gray-600 dark:text-gray-400 w-24">
                        Perusahaan:
                      </span>
                      <span className="text-sm text-gray-900 dark:text-white">
                        {supplier.companyName}
                      </span>
                    </div>
                  )}
                </div>
              </div>

              {/* Contact Information */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium text-gray-900 dark:text-white border-b border-gray-200 dark:border-gray-700 pb-2">
                  Informasi Kontak
                </h3>

                <div className="space-y-3">
                  <div className="flex items-center gap-3">
                    <Mail className="h-4 w-4 text-gray-400" />
                    <span className="text-sm text-gray-600 dark:text-gray-400 w-24">
                      Email:
                    </span>
                    <span className="text-sm text-gray-900 dark:text-white">
                      {supplier.email || "-"}
                    </span>
                  </div>

                  <div className="flex items-center gap-3">
                    <Phone className="h-4 w-4 text-gray-400" />
                    <span className="text-sm text-gray-600 dark:text-gray-400 w-24">
                      HP:
                    </span>
                    <span className="text-sm text-gray-900 dark:text-white">
                      {supplier.phone || "-"}
                    </span>
                  </div>

                  {supplier.telephone && (
                    <div className="flex items-center gap-3">
                      <PhoneCall className="h-4 w-4 text-gray-400" />
                      <span className="text-sm text-gray-600 dark:text-gray-400 w-24">
                        Telepon:
                      </span>
                      <span className="text-sm text-gray-900 dark:text-white">
                        {supplier.telephone}
                      </span>
                    </div>
                  )}

                  {supplier.fax && (
                    <div className="flex items-center gap-3">
                      <PhoneCall className="h-4 w-4 text-gray-400" />
                      <span className="text-sm text-gray-600 dark:text-gray-400 w-24">
                        Fax:
                      </span>
                      <span className="text-sm text-gray-900 dark:text-white">
                        {supplier.fax}
                      </span>
                    </div>
                  )}
                </div>
              </div>

              {/* Identity Information */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium text-gray-900 dark:text-white border-b border-gray-200 dark:border-gray-700 pb-2">
                  Informasi Identitas
                </h3>

                <div className="space-y-3">
                  {supplier.identityType && (
                    <div className="flex items-center gap-3">
                      <IdCard className="h-4 w-4 text-gray-400" />
                      <span className="text-sm text-gray-600 dark:text-gray-400 w-24">
                        Jenis ID:
                      </span>
                      <span className="text-sm text-gray-900 dark:text-white">
                        {supplier.identityType}
                      </span>
                    </div>
                  )}

                  {supplier.identityNumber && (
                    <div className="flex items-center gap-3">
                      <IdCard className="h-4 w-4 text-gray-400" />
                      <span className="text-sm text-gray-600 dark:text-gray-400 w-24">
                        Nomor ID:
                      </span>
                      <span className="text-sm text-gray-900 dark:text-white">
                        {supplier.identityNumber}
                      </span>
                    </div>
                  )}

                  {supplier.NIK && (
                    <div className="flex items-center gap-3">
                      <UserCheck className="h-4 w-4 text-gray-400" />
                      <span className="text-sm text-gray-600 dark:text-gray-400 w-24">
                        NIK:
                      </span>
                      <span className="text-sm text-gray-900 dark:text-white">
                        {supplier.NIK}
                      </span>
                    </div>
                  )}

                  {supplier.NPWP && (
                    <div className="flex items-center gap-3">
                      <CreditCard className="h-4 w-4 text-gray-400" />
                      <span className="text-sm text-gray-600 dark:text-gray-400 w-24">
                        NPWP:
                      </span>
                      <span className="text-sm text-gray-900 dark:text-white">
                        {supplier.NPWP}
                      </span>
                    </div>
                  )}
                </div>
              </div>

              {/* Address Information */}
              <div className="space-y-4 lg:col-span-2">
                <h3 className="text-lg font-medium text-gray-900 dark:text-white border-b border-gray-200 dark:border-gray-700 pb-2">
                  Informasi Alamat
                </h3>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-3">
                    {supplier.billingAddress && (
                      <div className="flex items-start gap-3">
                        <Home className="h-4 w-4 text-gray-400 mt-0.5" />
                        <span className="text-sm text-gray-600 dark:text-gray-400 w-24">
                          Alamat Tagihan:
                        </span>
                        <span className="text-sm text-gray-900 dark:text-white">
                          {supplier.billingAddress}
                        </span>
                      </div>
                    )}

                    {supplier.shippingAddress && (
                      <div className="flex items-start gap-3">
                        <Truck className="h-4 w-4 text-gray-400 mt-0.5" />
                        <span className="text-sm text-gray-600 dark:text-gray-400 w-24">
                          Alamat Kirim:
                        </span>
                        <span className="text-sm text-gray-900 dark:text-white">
                          {supplier.shippingAddress}
                        </span>
                      </div>
                    )}

                    {supplier.address && (
                      <div className="flex items-start gap-3">
                        <Home className="h-4 w-4 text-gray-400 mt-0.5" />
                        <span className="text-sm text-gray-600 dark:text-gray-400 w-24">
                          Alamat:
                        </span>
                        <span className="text-sm text-gray-900 dark:text-white">
                          {supplier.address}
                        </span>
                      </div>
                    )}
                  </div>
                </div>
              </div>

              {/* Bank Information */}
              <div className="space-y-3">
                <h3 className="text-lg font-medium text-gray-900 dark:text-white border-b border-gray-200 dark:border-gray-700 pb-2">
                  Informasi Bank
                </h3>

                {supplier.bankName && (
                  <div className="flex items-center gap-3">
                    <Banknote className="h-4 w-4 text-gray-400" />
                    <span className="text-sm text-gray-600 dark:text-gray-400 w-24">
                      Bank:
                    </span>
                    <span className="text-sm text-gray-900 dark:text-white">
                      {supplier.bankName}
                    </span>
                  </div>
                )}

                {supplier.bankBranch && (
                  <div className="flex items-center gap-3">
                    <Banknote className="h-4 w-4 text-gray-400" />
                    <span className="text-sm text-gray-600 dark:text-gray-400 w-24">
                      Cabang:
                    </span>
                    <span className="text-sm text-gray-900 dark:text-white">
                      {supplier.bankBranch}
                    </span>
                  </div>
                )}

                {supplier.accountHolder && (
                  <div className="flex items-center gap-3">
                    <User className="h-4 w-4 text-gray-400" />
                    <span className="text-sm text-gray-600 dark:text-gray-400 w-24">
                      Pemegang:
                    </span>
                    <span className="text-sm text-gray-900 dark:text-white">
                      {supplier.accountHolder}
                    </span>
                  </div>
                )}

                {supplier.accountNumber && (
                  <div className="flex items-center gap-3">
                    <CreditCard className="h-4 w-4 text-gray-400" />
                    <span className="text-sm text-gray-600 dark:text-gray-400 w-24">
                      No. Rekening:
                    </span>
                    <span className="text-sm text-gray-900 dark:text-white font-mono">
                      {supplier.accountNumber}
                    </span>
                  </div>
                )}
              </div>

              {/* Additional Information */}
              <div className="space-y-4 lg:col-span-2">
                <h3 className="text-lg font-medium text-gray-900 dark:text-white border-b border-gray-200 dark:border-gray-700 pb-2">
                  Informasi Tambahan
                </h3>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-3">
                    {supplier.otherInfo && (
                      <div className="flex items-start gap-3">
                        <FileText className="h-4 w-4 text-gray-400 mt-0.5" />
                        <span className="text-sm text-gray-600 dark:text-gray-400 w-24">
                          Info Lain:
                        </span>
                        <span className="text-sm text-gray-900 dark:text-white">
                          {supplier.otherInfo}
                        </span>
                      </div>
                    )}

                    <div className="flex items-start gap-3">
                      <FileText className="h-4 w-4 text-gray-400 mt-0.5" />
                      <span className="text-sm text-gray-600 dark:text-gray-400 w-24">
                        Catatan:
                      </span>
                      <span className="text-sm text-gray-900 dark:text-white">
                        {supplier.notes || "-"}
                      </span>
                    </div>

                    <div className="flex items-center gap-3">
                      <Calendar className="h-4 w-4 text-gray-400" />
                      <span className="text-sm text-gray-600 dark:text-gray-400 w-24">
                        Dibuat:
                      </span>
                      <span className="text-sm text-gray-900 dark:text-white">
                        {format(
                          new Date(supplier.createdAt),
                          "dd MMMM yyyy 'pukul' HH:mm",
                          { locale: id }
                        )}
                      </span>
                    </div>

                    <div className="flex items-center gap-3">
                      <Calendar className="h-4 w-4 text-gray-400" />
                      <span className="text-sm text-gray-600 dark:text-gray-400 w-24">
                        Diperbarui:
                      </span>
                      <span className="text-sm text-gray-900 dark:text-white">
                        {format(
                          new Date(supplier.updatedAt),
                          "dd MMMM yyyy 'pukul' HH:mm",
                          { locale: id }
                        )}
                      </span>
                    </div>
                  </div>

                  <div className="space-y-3">
                    <div className="flex items-center gap-3">
                      <span className="text-sm text-gray-600 dark:text-gray-400 w-24">
                        ID Supplier:
                      </span>
                      <span className="text-sm text-gray-900 dark:text-white font-mono">
                        {supplier.id}
                      </span>
                    </div>

                    <div className="flex items-center gap-3">
                      <span className="text-sm text-gray-600 dark:text-gray-400 w-24">
                        Status:
                      </span>
                      <Badge className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                        Aktif
                      </Badge>
                    </div>

                    <div className="flex items-center gap-3">
                      <span className="text-sm text-gray-600 dark:text-gray-400 w-24">
                        Tipe Kontak:
                      </span>
                      <Badge className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                        Supplier
                      </Badge>
                    </div>

                    {supplier.sameAsShipping && (
                      <div className="flex items-center gap-3">
                        <span className="text-sm text-gray-600 dark:text-gray-400 w-24">
                          Alamat Sama:
                        </span>
                        <Badge className="bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200">
                          Ya
                        </Badge>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SupplierDetailPage;
