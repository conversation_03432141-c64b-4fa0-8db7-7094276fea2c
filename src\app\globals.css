@import "tailwindcss";
@plugin "tailwindcss-animate";

html,
body,
:root {
  height: 100%;
}

body {
  font-size: 16px; /* Default font size */
  font-family: var(--font-inter), Inter, system-ui, sans-serif;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

:root {
  --radius: 0.625rem;
  --background: oklch(1 0 0);
  --foreground: oklch(0.145 0 0);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);
  --primary: oklch(0.205 0 0);
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.97 0 0);
  --secondary-foreground: oklch(0.205 0 0);
  --muted: oklch(0.97 0 0);
  --muted-foreground: oklch(0.556 0 0);
  --accent: oklch(0.97 0 0);
  --accent-foreground: oklch(0.205 0 0);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.922 0 0);
  --input: oklch(0.922 0 0);
  --ring: oklch(0.708 0 0);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: oklch(0.205 0 0);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.97 0 0);
  --sidebar-accent-foreground: oklch(0.205 0 0);
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: oklch(0.708 0 0);
}

.dark {
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.205 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.205 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.922 0 0);
  --primary-foreground: oklch(0.205 0 0);
  --secondary: oklch(0.269 0 0);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.708 0 0);
  --accent: oklch(0.269 0 0);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.704 0.191 22.216);
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(0.556 0 0);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.556 0 0);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  html {
    @apply text-base font-sans; /* Use Tailwind's text-base and font-sans classes */
  }
  body {
    @apply bg-background text-foreground font-sans;
  }
}

/* Awal CSS untuk Not Found Page */
@custom-variant dark (&:is(.dark *));

@keyframes glitch-1 {
  0%,
  100% {
    transform: none;
    opacity: 0;
  }
  50% {
    transform: translate(-2px, 2px);
    opacity: 0.3;
  }
}

@keyframes glitch-2 {
  0%,
  100% {
    transform: none;
    opacity: 0;
  }
  50% {
    transform: translate(2px, -2px);
    opacity: 0.3;
  }
}

@keyframes glitch-3 {
  0%,
  100% {
    transform: none;
    opacity: 0;
  }
  50% {
    transform: translate(-2px, -2px);
    opacity: 0.3;
  }
}

.animate-glitch-1 {
  animation: glitch-1 3s infinite;
}

.animate-glitch-2 {
  animation: glitch-2 3s infinite;
  animation-delay: -1s;
}

.animate-glitch-3 {
  animation: glitch-3 3s infinite;
  animation-delay: -2s;
}
/* Akhir CSS untuk Not Found Page */

/* Hide scrollbar but keep functionality */
.hide-scrollbar {
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
}

.hide-scrollbar::-webkit-scrollbar {
  display: none; /* Chrome, Safari and Opera */
}

/* Custom thin scrollbar */
.thin-scrollbar {
  scrollbar-width: thin; /* Firefox */
  scrollbar-color: rgba(156, 163, 175, 0.3) transparent; /* Firefox */
}

.thin-scrollbar::-webkit-scrollbar {
  width: 4px; /* Width of the scrollbar */
  height: 4px; /* Height of the scrollbar for horizontal scrolling */
}

.thin-scrollbar::-webkit-scrollbar-track {
  background: transparent; /* Track background */
}

.thin-scrollbar::-webkit-scrollbar-thumb {
  background-color: rgba(156, 163, 175, 0.3); /* Thumb color */
  border-radius: 20px; /* Rounded corners */
}

.thin-scrollbar::-webkit-scrollbar-thumb:hover {
  background-color: rgba(156, 163, 175, 0.5); /* Thumb color on hover */
}

/* Dark mode adjustments */
.dark .thin-scrollbar {
  scrollbar-color: rgba(75, 85, 99, 0.4) transparent; /* Firefox */
}

.dark .thin-scrollbar::-webkit-scrollbar-thumb {
  background-color: rgba(75, 85, 99, 0.4); /* Thumb color in dark mode */
}

.dark .thin-scrollbar::-webkit-scrollbar-thumb:hover {
  background-color: rgba(
    75,
    85,
    99,
    0.6
  ); /* Thumb color on hover in dark mode */
}

/* Custom styles for Layanan menu in collapsed sidebar */
.layanan-menu:hover + .layanan-submenu,
.layanan-submenu:hover {
  opacity: 1 !important;
  pointer-events: auto !important;
  transform: scale(1) !important;
  display: block !important;
}

/* Ensure the Layanan submenu is visible on hover */
.group:hover .layanan-submenu {
  opacity: 1 !important;
  pointer-events: auto !important;
  transform: scale(1) !important;
  display: block !important;
}

/* Fix for Layanan menu dropdown positioning */
.layanan-menu {
  position: relative;
}

/* Ensure Layanan submenu is on top of everything */
.layanan-submenu {
  z-index: 9999 !important;
}
