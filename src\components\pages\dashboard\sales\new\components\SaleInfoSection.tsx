import React, { useEffect, useState } from "react";
import { Control } from "react-hook-form";
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  FormDescription,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { DatePicker } from "@/components/ui/date-picker";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { SaleFormValues, Customer } from "../types";
import {
  getNextTransactionNumber,
  getNextInvoiceNumber,
} from "@/actions/entities/sales";
import {
  Calendar,
  Mail,
  Tag,
  Home,
  Warehouse,
  Hash,
  FileText,
  User,
  X,
  Settings,
  Loader2,
  Clock,
  Plus,
  Search,
  ChevronsUpDown,
  RefreshCw,
} from "lucide-react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { getCustomersAction } from "@/actions/entities/get-customers-action";

interface SaleInfoSectionProps {
  control: Control<SaleFormValues>;
  isPending: boolean;
  createdAt?: string; // Optional createdAt for edit mode
}

// No default customer

const SaleInfoSection: React.FC<SaleInfoSectionProps> = ({
  control,
  isPending,
}) => {
  // State for auto-generation settings
  const [autoTransactionNumber, setAutoTransactionNumber] =
    React.useState(false);
  const [tagInput, setTagInput] = React.useState("");

  // State for actual next transaction numbers
  const [nextTrxNumber, setNextTrxNumber] = React.useState<string>("");
  const [nextInvNumber, setNextInvNumber] = React.useState<string>("");
  const [isLoadingTrx, setIsLoadingTrx] = React.useState(false);
  const [isLoadingInv, setIsLoadingInv] = React.useState(false);
  const [customers, setCustomers] = React.useState<Customer[]>([]);
  const [isLoadingCustomers, setIsLoadingCustomers] = React.useState(true);

  // State for customer dropdown
  const [customerSearchTerm, setCustomerSearchTerm] = useState("");
  const [customerDropdownOpen, setCustomerDropdownOpen] = useState(false);

  // Enhanced filtering - search across multiple fields
  const filteredCustomers = customers.filter((customer) => {
    const searchLower = customerSearchTerm.toLowerCase().trim();
    if (!searchLower) return true;

    return (
      customer.name.toLowerCase().includes(searchLower) ||
      (customer.email &&
        customer.email !== "-" &&
        customer.email.toLowerCase().includes(searchLower)) ||
      (customer.phone &&
        customer.phone !== "-" &&
        customer.phone.toLowerCase().includes(searchLower)) ||
      (customer.NIK &&
        customer.NIK !== "-" &&
        customer.NIK.toLowerCase().includes(searchLower)) ||
      (customer.NPWP &&
        customer.NPWP !== "-" &&
        customer.NPWP.toLowerCase().includes(searchLower))
    );
  });

  // Generate fallback example transaction numbers for placeholders
  const currentDate = new Date();
  const year = currentDate.getFullYear();
  const yearSuffix = String(year).slice(-2);
  const fallbackTrxNumber = `TRX-${year}-J000001`;
  const fallbackInvNumber = `INV-${yearSuffix}J000001`;

  // Fetch customers
  useEffect(() => {
    const fetchCustomers = async () => {
      try {
        setIsLoadingCustomers(true);
        const result = await getCustomersAction();

        if (result.success && result.customers) {
          // Map the customers to include NIK and NPWP fields
          const mappedCustomers = result.customers.map((customer) => ({
            id: customer.id,
            name: customer.name,
            phone: customer.phone || "-",
            email: customer.email || "-",
            address: customer.address || "-",
            NIK: customer.NIK || "-",
            NPWP: customer.NPWP || "-",
          }));

          // Set customers directly without default customer
          setCustomers(mappedCustomers);
        }
      } catch (error) {
        console.error("Error fetching customers:", error);
      } finally {
        setIsLoadingCustomers(false);
      }
    };

    fetchCustomers();
  }, []);

  // Function to fetch next transaction number
  const fetchNextTransactionNumber = async () => {
    try {
      setIsLoadingTrx(true);
      const result = await getNextTransactionNumber("TRX");
      if (result.success && result.nextNumber) {
        setNextTrxNumber(result.nextNumber);
      }
    } catch (error) {
      console.error("Error fetching next transaction number:", error);
    } finally {
      setIsLoadingTrx(false);
    }
  };

  // Function to fetch next invoice number
  const fetchNextInvoiceNumber = async () => {
    try {
      setIsLoadingInv(true);
      const result = await getNextInvoiceNumber();
      if (result.success && result.nextNumber) {
        setNextInvNumber(result.nextNumber);
      }
    } catch (error) {
      console.error("Error fetching next invoice number:", error);
    } finally {
      setIsLoadingInv(false);
    }
  };

  // Fetch next transaction number when auto-generation is toggled
  useEffect(() => {
    if (autoTransactionNumber && !nextTrxNumber) {
      fetchNextTransactionNumber();
    }
  }, [autoTransactionNumber, nextTrxNumber]);

  // Fetch next invoice number when auto-generation is toggled
  useEffect(() => {
    if (autoInvoiceRef && !nextInvNumber) {
      fetchNextInvoiceNumber();
    }
  }, [autoInvoiceRef, nextInvNumber]);

  // No need for separate tag handling functions as they're now included in the FormField render prop

  return (
    <div className="space-y-6">
      {/* Main Fields - Horizontal Layout */}
      <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
        {/* Pelanggan */}
        <FormField
          control={control}
          name="customerId"
          render={({ field }) => (
            <FormItem className="col-span-1">
              <FormLabel className="flex items-center gap-1.5">
                <User className="h-4 w-4 text-blue-600" />
                Pelanggan
              </FormLabel>
              <Popover
                open={customerDropdownOpen}
                onOpenChange={setCustomerDropdownOpen}
              >
                <PopoverTrigger asChild>
                  <FormControl>
                    <Button
                      variant="outline"
                      role="combobox"
                      aria-expanded={customerDropdownOpen}
                      className={`w-full justify-between hover:bg-accent hover:text-accent-foreground ${
                        !field.value ? "text-muted-foreground" : ""
                      }`}
                      disabled={isPending}
                    >
                      {isLoadingCustomers ? (
                        <div className="flex items-center">
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          Memuat pelanggan...
                        </div>
                      ) : field.value ? (
                        <div className="flex items-center">
                          <User className="mr-2 h-4 w-4" />
                          <span>
                            {customers.find(
                              (customer) => customer.id === field.value
                            )?.name || "Pilih pelanggan"}
                          </span>
                        </div>
                      ) : (
                        <div className="flex items-center">
                          <User className="mr-2 h-4 w-4" />
                          <span>Pilih pelanggan</span>
                        </div>
                      )}
                      <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                    </Button>
                  </FormControl>
                </PopoverTrigger>
                <PopoverContent className="w-[var(--radix-popover-trigger-width)] p-0">
                  <Command shouldFilter={false}>
                    <CommandInput
                      placeholder="Cari pelanggan..."
                      value={customerSearchTerm}
                      onValueChange={setCustomerSearchTerm}
                      className="h-9"
                      onFocus={() => {
                        if (!customerDropdownOpen) {
                          setCustomerDropdownOpen(true);
                        }
                      }}
                    />
                    <CommandList className="max-h-[300px] overflow-y-auto">
                      <CommandGroup>
                        {/* Show all filtered customers or just 3 if not searching */}
                        {(customerSearchTerm
                          ? filteredCustomers
                          : customers.slice(0, 3)
                        ).map((customer) => (
                          <div key={customer.id} className="px-2">
                            <button
                              type="button"
                              className="w-full flex items-center text-left rounded-md px-3 py-3 text-sm bg-transparent hover:bg-accent hover:text-accent-foreground cursor-pointer transition-colors border border-transparent hover:border-gray-200 focus:bg-accent focus:text-accent-foreground focus:outline-none"
                              onClick={() => {
                                field.onChange(customer.id);
                                setCustomerDropdownOpen(false);
                                setCustomerSearchTerm("");

                                // Update email field if available
                                if (customer.email && customer.email !== "-") {
                                  // Use the proper form API to set the value
                                  const emailField = document.querySelector(
                                    'input[name="customerEmail"]'
                                  ) as HTMLInputElement;

                                  if (emailField) {
                                    emailField.value = customer.email;
                                    // Trigger a change event to update the form
                                    const event = new Event("input", {
                                      bubbles: true,
                                    });
                                    emailField.dispatchEvent(event);
                                  }
                                } else {
                                  // Clear the email field
                                  const emailField = document.querySelector(
                                    'input[name="customerEmail"]'
                                  ) as HTMLInputElement;

                                  if (emailField) {
                                    emailField.value = "";
                                    // Trigger a change event to update the form
                                    const event = new Event("input", {
                                      bubbles: true,
                                    });
                                    emailField.dispatchEvent(event);
                                  }
                                }
                              }}
                            >
                              <User className="mr-2 h-4 w-4 shrink-0 text-blue-500" />
                              <div className="flex flex-col flex-grow">
                                <span className="font-medium">
                                  {customer.name}
                                </span>
                                {customer.email && customer.email !== "-" && (
                                  <span className="text-xs text-muted-foreground">
                                    {customer.email}
                                  </span>
                                )}
                              </div>
                              <span className="text-xs text-blue-500 ml-2">
                                Pilih
                              </span>
                            </button>
                          </div>
                        ))}
                      </CommandGroup>
                    </CommandList>
                  </Command>
                </PopoverContent>
              </Popover>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Email */}
        <FormField
          control={control}
          name="customerEmail"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="flex items-center gap-1.5">
                <Mail className="h-4 w-4 text-red-600" />
                Email
              </FormLabel>
              <FormControl>
                <Input
                  placeholder="Enter email"
                  {...field}
                  disabled={isPending}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Transaction Date */}
        <FormField
          control={control}
          name="transactionDate"
          render={({ field: { value, onChange } }) => (
            <FormItem>
              <FormLabel className="flex items-center gap-1.5">
                <Calendar className="h-4 w-4 text-green-600" />
                Tgl. Transaksi
              </FormLabel>
              <FormControl>
                <DatePicker
                  date={value ? new Date(value) : new Date()}
                  setDate={(date) => onChange(date || new Date())}
                  placeholder="Pilih tanggal transaksi"
                  disabled={isPending}
                  className="w-full"
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Payment Due Date */}
        <FormField
          control={control}
          name="paymentDueDate"
          render={({ field: { value, onChange } }) => (
            <FormItem>
              <FormLabel className="flex items-center gap-1.5">
                <Clock className="h-4 w-4 text-yellow-600" />
                Tgl. Jatuh Tempo
              </FormLabel>
              <FormControl>
                <DatePicker
                  date={value ? new Date(value) : undefined}
                  setDate={(date) => onChange(date)}
                  placeholder="Pilih tanggal jatuh tempo"
                  disabled={isPending}
                  className="w-full"
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Transaction Number */}
        <FormField
          control={control}
          name="transactionNumber"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="flex items-center gap-1.5">
                <Hash className="h-4 w-4 text-purple-600" />
                Nomor Transaksi
              </FormLabel>
              <div className="relative">
                <FormControl>
                  <Input
                    placeholder={
                      autoTransactionNumber
                        ? isLoadingTrx
                          ? "Loading..."
                          : `Auto - ${nextTrxNumber || fallbackTrxNumber}`
                        : `Contoh: TRX-${year}-J000001`
                    }
                    {...field}
                    disabled={isPending || autoTransactionNumber}
                    value={autoTransactionNumber ? "" : field.value}
                    onChange={(e) => {
                      if (!autoTransactionNumber) {
                        field.onChange(e.target.value);
                      }
                    }}
                  />
                </FormControl>
                {isLoadingTrx && autoTransactionNumber ? (
                  <div className="absolute right-2 top-1/2 -translate-y-1/2 text-gray-500">
                    <Loader2 className="h-4 w-4 animate-spin" />
                  </div>
                ) : (
                  <button
                    type="button"
                    className="absolute right-2 top-1/2 -translate-y-1/2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 cursor-pointer"
                    onClick={() => {
                      setAutoTransactionNumber(!autoTransactionNumber);
                      if (!autoTransactionNumber) {
                        // When enabling auto, clear the field
                        field.onChange("");
                      }
                    }}
                    title={
                      autoTransactionNumber
                        ? "Disable auto-generation"
                        : "Enable auto-generation"
                    }
                  >
                    <Settings className="h-4 w-4" />
                  </button>
                )}
              </div>
              {autoTransactionNumber && (
                <p className="text-xs text-muted-foreground mt-1">
                  Nomor transaksi akan digenerate otomatis
                </p>
              )}
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Invoice Reference */}
        <FormField
          control={control}
          name="invoiceRef"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="flex items-center gap-1.5">
                <FileText className="h-4 w-4 text-yellow-600" />
                Nomor Invoice
              </FormLabel>
              <div className="relative">
                <FormControl>
                  <Input
                    placeholder={
                      autoInvoiceRef
                        ? isLoadingInv
                          ? "Loading..."
                          : `Auto - ${nextInvNumber || fallbackInvNumber}`
                        : `Contoh: INV-${year}-J000001`
                    }
                    {...field}
                    disabled={isPending || autoInvoiceRef}
                    value={autoInvoiceRef ? "" : field.value}
                    onChange={(e) => {
                      if (!autoInvoiceRef) {
                        field.onChange(e.target.value);
                      }
                    }}
                  />
                </FormControl>
                {isLoadingInv && autoInvoiceRef ? (
                  <div className="absolute right-2 top-1/2 -translate-y-1/2 text-gray-500">
                    <Loader2 className="h-4 w-4 animate-spin" />
                  </div>
                ) : (
                  <button
                    type="button"
                    className="absolute right-2 top-1/2 -translate-y-1/2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 cursor-pointer"
                    onClick={() => {
                      setAutoInvoiceRef(!autoInvoiceRef);
                      if (!autoInvoiceRef) {
                        // When enabling auto, clear the field
                        field.onChange("");
                      }
                    }}
                    title={
                      autoInvoiceRef
                        ? "Disable auto-generation"
                        : "Enable auto-generation"
                    }
                  >
                    <Settings className="h-4 w-4" />
                  </button>
                )}
              </div>
              {autoInvoiceRef && (
                <p className="text-xs text-muted-foreground mt-1">
                  Nomor invoice akan digenerate otomatis
                </p>
              )}
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Warehouse */}
        <FormField
          control={control}
          name="warehouse"
          render={({ field }) => (
            <FormItem className="col-span-1">
              <FormLabel className="flex items-center gap-1.5">
                <Warehouse className="h-4 w-4 text-violet-600" />
                Gudang
              </FormLabel>
              <Select
                disabled={isPending}
                onValueChange={field.onChange}
                defaultValue={field.value || ""}
              >
                <FormControl>
                  <SelectTrigger className="w-full">
                    <SelectValue placeholder="Pilih gudang" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  <SelectItem value="gudang-pusat">Gudang Pusat</SelectItem>
                  <SelectItem value="gudang-cabang">Gudang Cabang</SelectItem>
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Tags */}
        <FormField
          control={control}
          name="tags"
          render={({ field }) => {
            const handleAddTag = () => {
              if (tagInput.trim()) {
                // Check if the tag already exists
                if (!field.value?.includes(tagInput.trim())) {
                  const newTags = [...(field.value || []), tagInput.trim()];
                  field.onChange(newTags);
                }
                setTagInput("");
              }
            };

            const handleKeyDown = (e: React.KeyboardEvent) => {
              if (e.key === "Enter" || e.key === ",") {
                e.preventDefault();

                // If comma is pressed, we might have multiple tags
                if (e.key === ",") {
                  const tags = tagInput
                    .split(",")
                    .map((tag) => tag.trim())
                    .filter((tag) => tag !== "");

                  if (tags.length > 0) {
                    // Add all tags that don't already exist
                    const existingTags = field.value || [];
                    const newTags = [...existingTags];

                    tags.forEach((tag) => {
                      if (!existingTags.includes(tag)) {
                        newTags.push(tag);
                      }
                    });

                    field.onChange(newTags);
                    setTagInput("");
                    return;
                  }
                }

                handleAddTag();
              }
            };

            return (
              <FormItem>
                <FormLabel className="flex items-center gap-1.5">
                  <Tag className="h-4 w-4 text-teal-600" />
                  Tag
                </FormLabel>

                {/* Input for new tags - Moved to top */}
                <div className="flex gap-2 mb-3">
                  <FormControl>
                    <Input
                      placeholder="Tambahkan tag (tekan Enter atau koma)"
                      value={tagInput}
                      onChange={(e) => setTagInput(e.target.value)}
                      onKeyDown={handleKeyDown}
                      disabled={isPending}
                      className="flex-1"
                    />
                  </FormControl>
                  <Button
                    type="button"
                    size="sm"
                    variant="outline"
                    onClick={handleAddTag}
                    disabled={isPending || !tagInput.trim()}
                    className="border-teal-200 hover:bg-teal-100 hover:text-teal-800 cursor-pointer"
                  >
                    <Plus className="h-4 w-4 mr-1" />
                    Tambah
                  </Button>
                </div>

                {/* Display existing tags - Moved to bottom */}
                <div className="flex flex-wrap gap-2">
                  {field.value && field.value.length > 0 ? (
                    field.value.map((tag, index) => (
                      <Badge
                        key={index}
                        variant="secondary"
                        className="bg-teal-100 text-teal-800 dark:bg-teal-900/30 dark:text-teal-300 flex items-center gap-1 shadow-sm"
                      >
                        {tag}
                        <button
                          type="button"
                          className="ml-1 hover:text-red-500 cursor-pointer"
                          onClick={() => {
                            const newTags = [...(field.value || [])];
                            newTags.splice(index, 1);
                            field.onChange(newTags);
                          }}
                          disabled={isPending}
                        >
                          <X className="h-3 w-3" />
                        </button>
                      </Badge>
                    ))
                  ) : (
                    <span className="text-sm text-muted-foreground">
                      Belum ada tag
                    </span>
                  )}
                </div>

                <FormDescription>
                  Tag untuk memudahkan pencarian dan pengelompokan
                </FormDescription>
                <FormMessage />
              </FormItem>
            );
          }}
        />
      </div>

      {/* Shipping Address - Full Width */}
      <div>
        <FormField
          control={control}
          name="shippingAddress"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="flex items-center gap-1.5">
                <Home className="h-4 w-4 text-orange-600" />
                Alamat pengiriman
              </FormLabel>
              <FormControl>
                <Textarea
                  placeholder="e.g. Jalan Indonesia Blok C No. 22"
                  className="resize-none"
                  {...field}
                  disabled={isPending}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>
    </div>
  );
};

export default SaleInfoSection;
