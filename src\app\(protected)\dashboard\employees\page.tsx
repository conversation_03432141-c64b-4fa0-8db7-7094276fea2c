import React from "react";
import { Metadata } from "next";
import DashboardLayout from "@/components/layout/dashboardlayout";
import EmployeesPage from "@/components/pages/dashboard/employees/employees";
import { auth } from "@/lib/auth";
import { redirect } from "next/navigation";
import { getEmployees } from "@/actions/entities/employee";

export const metadata: Metadata = {
  title: "Karyawan | Kasir Online",
  description: "Kelola data karyawan dan hak akses sistem",
};

const Employees = async () => {
  const session = await auth();
  if (!session?.user?.id) {
    redirect("/login");
  }

  // Fetch employee data
  const employeesResult = await getEmployees();

  if (employeesResult.error) {
    console.error("Error fetching employees:", employeesResult.error);
  }

  return (
    <DashboardLayout>
      <EmployeesPage employees={employeesResult.employees || []} />
    </DashboardLayout>
  );
};

export default Employees;
